using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using static System.Console;

namespace tui;

internal class Program
{
    // Add P/Invoke declarations for console font manipulation
    [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
    private struct ConsoleFontInfoEx
    {
        public uint cbSize;
        public uint nFont;
        public short FontWidth;
        public short FontHeight;
        public int FontFamily;
        public int FontWeight;
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
        public string FaceName;
    }

    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern bool SetCurrentConsoleFontEx(
        IntPtr consoleOutput,
        bool maximumWindow,
        ref ConsoleFontInfoEx consoleCurrentFontEx);

    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern IntPtr GetStdHandle(int nStdHandle);

    private const int StdOutputHandle = -11;
    private const int TmpfTruetype = 4;

    private static void SetConsoleFont(string fontName = "Consolas", short fontSize = 16)
    {
        // Only attempt to set font on Windows
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows)) return;
        try
        {
            var hConsoleOutput = GetStdHandle(StdOutputHandle);
            var fontInfo = new ConsoleFontInfoEx();
            fontInfo.cbSize = (uint)Marshal.SizeOf(fontInfo);
            fontInfo.FontFamily = TmpfTruetype;
            fontInfo.FaceName = fontName;
            fontInfo.FontWidth = 0; // Let the system decide based on height
            fontInfo.FontHeight = fontSize;
            fontInfo.FontWeight = 400; // Normal weight
                
            SetCurrentConsoleFontEx(hConsoleOutput, false, ref fontInfo);
        }
        catch (Exception ex)
        {
            WriteLine($"Note: Could not set console font: {ex.Message}");
        }
    }

    private static readonly string[] LogoBase =
    [
        " _____                                                                  _____ ",
        "( ___ )                                                                ( ___ )",
        " |   |~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~|   | ",
        " |   |          ██████╗ ██╗██╗   ██╗██╗██████╗ ███████╗                 |   | ",
        " |   |          ██╔══██╗██║██║   ██║██║██╔══██╗██╔════╝                 |   | ",
        " |   |          ██║  ██║██║██║   ██║██║██║  ██║█████╗                   |   | ",
        " |   |          ██║  ██║██║╚██╗ ██╔╝██║██║  ██║██╔══╝                   |   | ",
        " |   |          ██████╔╝██║ ╚████╔╝ ██║██████╔╝███████╗                 |   | ",
        " |   |          ╚═════╝ ╚═╝  ╚═══╝  ╚═╝╚═════╝ ╚══════╝                 |   | ",
        " |   |                      ██████╗ ██╗   ██╗                           |   | ",
        " |   |                      ██╔══██╗╚██╗ ██╔╝                           |   | ",
        " |   |                      ██████╔╝ ╚████╔╝                            |   | ",
        " |   |                      ██╔══██╗  ╚██╔╝                             |   | ",
        " |   |                      ██████╔╝   ██║                              |   | ",
        " |   |                      ╚═════╝    ╚═╝                              |   | ",
        " |   |              ███████╗███████╗██████╗  ██████╗                    |   | ",
        " |   |              ╚══███╔╝██╔════╝██╔══██╗██╔═══██╗                   |   | ",
        " |   |                ███╔╝ █████╗  ██████╔╝██║   ██║                   |   | ",
        " |   |               ███╔╝  ██╔══╝  ██╔══██╗██║   ██║                   |   | ",
        " |   |              ███████╗███████╗██║  ██║╚██████╔╝                   |   | ",
        " |   |              ╚══════╝╚══════╝╚═╝  ╚═╝ ╚═════╝                    |   | ",
        " |   |      ███████╗████████╗██╗   ██╗██████╗ ██╗ ██████╗ ███████╗      |   | ",
        " |   |      ██╔════╝╚══██╔══╝██║   ██║██╔══██╗██║██╔═══██╗██╔════╝      |   | ",
        " |   |      ███████╗   ██║   ██║   ██║██║  ██║██║██║   ██║███████╗      |   | ",
        " |   |      ╚════██║   ██║   ██║   ██║██║  ██║██║██║   ██║╚════██║      |   | ",
        " |   |      ███████║   ██║   ╚██████╔╝██████╔╝██║╚██████╔╝███████║      |   | ",
        " |   |      ╚══════╝   ╚═╝    ╚═════╝ ╚═════╝ ╚═╝ ╚═════╝ ╚══════╝      |   | ",
        " |___|~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~|___| ",
        "(_____)                                                                (_____)"
    ];

    private static readonly ConsoleColor[] RainbowColors =
    [
        ConsoleColor.Red, ConsoleColor.Yellow, ConsoleColor.Green,
        ConsoleColor.Cyan, ConsoleColor.Blue, ConsoleColor.Magenta,
        ConsoleColor.DarkRed, ConsoleColor.DarkYellow, ConsoleColor.DarkGreen,
        ConsoleColor.DarkCyan, ConsoleColor.DarkBlue, ConsoleColor.DarkMagenta
    ];

    private static int _spinnerIndex = 0;
    private static readonly char[] SpinnerSequence = ['|', '/', '-', '\\'];

    private static void Main(string?[] args)
    {
        OutputEncoding = Encoding.UTF8;
        
        // Set fixed font
        SetConsoleFont("Consolas", 16);
        
        // Set fixed window size
        var logoWidth = LogoBase[0].Length;
        var logoHeight = LogoBase.Length;
        var windowWidth = Math.Max(logoWidth + 4, 120);
        var windowHeight = Math.Max(logoHeight + 20, 40);
        
        try
        {
            WindowWidth = windowWidth;
            WindowHeight = windowHeight;
            BufferWidth = windowWidth;
            BufferHeight = windowHeight;
        }
        catch (Exception ex)
        {
            WriteLine($"Note: Could not set window size: {ex.Message}");
        }
        
        ShowAnimatedLogo();

        string? targetDir = null;

        if (args.Length > 0)
        {
            if (args[0] == "--dir" && args.Length > 1)
                targetDir = args[1];
            else
                targetDir = args[0];
        }

        if (string.IsNullOrWhiteSpace(targetDir))
        {
            targetDir = PromptForDirectory();
        }

        if (!ValidateOrCreateDirectory(targetDir, out var validationError))
        {
            if (!string.IsNullOrEmpty(validationError))
                PrintError(validationError);

            PrintError($"Could not create or access directory: {targetDir}");
            Environment.Exit(1);
        }

        PrintInfo($"Using target directory: {targetDir}");

        try
        {
            CreateProjectDirectories(targetDir);

            CreateSharedLib(targetDir);
            CreateSamplePlugin(targetDir);
            CreateHostApp(targetDir);

            PrintInfo("Restoring and building projects...");
            if (!RunDotnetRestore(targetDir)) ExitWithError("dotnet restore failed.");
            if (!RunDotnetBuild(targetDir)) ExitWithError("dotnet build failed.");

            PrintSuccess("All projects created and built successfully! 🎉");
        }
        catch (Exception ex)
        {
            PrintError($"Unexpected error: {ex.Message}");
            Environment.Exit(1);
        }

        WriteLine("\nPress Enter to exit or type 'stay' to keep the window open:");
        var input = ReadLine()?.Trim().ToLower();
        if (input != "stay") return;
        WriteLine("Window will stay open. Press Ctrl+C to exit when done.");
        // Keep console window open indefinitely
        WriteLine("Press any key to exit when ready...");
        while (!KeyAvailable)
        {
            Thread.Sleep(100);
        }
    }

    private static void ShowAnimatedLogo()
    {
        // Expanding animation effect with single color
        var maxWidth = LogoBase[0].Length;
        var centerX = maxWidth / 2;
        var totalLines = LogoBase.Length;
        var centerY = totalLines / 2;
        const ConsoleColor logoColor = ConsoleColor.Cyan; // Single color for the entire animation
        
        // Start with a small portion of the logo and expand outward
        for (var expansion = 1; expansion <= Math.Max(centerX, centerY) + 5; expansion += 2) // Increment by 2 for faster expansion
        {
            Clear();
            
            for (var y = 0; y < totalLines; y++)
            {
                var line = LogoBase[y];
                var visibleLine = new StringBuilder(new string(' ', maxWidth));
                
                for (var x = 0; x < maxWidth; x++)
                {
                    // Calculate distance from center
                    var distanceFromCenter = (int)Math.Sqrt(
                        Math.Pow(x - centerX, 2) + 
                        Math.Pow((y - centerY) * 2, 2)); // Multiply y by 2 to account for character height/width ratio
                    
                    // If within the current expansion radius, show the character
                    if (distanceFromCenter > expansion * 3) continue;
                    if (x < line.Length)
                        visibleLine[x] = line[x];
                }
                
                WriteColoredLine(visibleLine.ToString(), logoColor);
            }
            
            Thread.Sleep(50); // Reduced delay for faster animation
        }
        
        // Final display with full logo
        Clear();
        foreach (var line in LogoBase)
        {
            WriteColoredLine(line, logoColor);
        }
    }

    private static string? PromptForDirectory()
    {
        Write("Enter target directory for your project setup: ");
        return ReadLine()?.Trim('"');
    }

    private static bool ValidateOrCreateDirectory(string? path, out string errorMessage)
    {
        errorMessage = string.Empty;
        try
        {
            if (Directory.Exists(path)) return true;
            if (path != null) Directory.CreateDirectory(path);
            return true;
        }
        catch (Exception ex)
        {
            errorMessage = ex switch
            {
                // Provide more specific error messages for common issues
                UnauthorizedAccessException => $"Permission denied to access or create directory: {path}",
                PathTooLongException => $"Path is too long: {path}",
                _ => $"Error validating or creating directory {path}: {ex.Message}"
            };
            return false;
        }
    }

    private static void CreateProjectDirectories(string? baseDir)
    {
        if (baseDir != null)
        {
            string[] subDirs =
            [
                Path.Combine(baseDir, "SharedLib"),
                Path.Combine(baseDir, "SamplePlugin"),
                Path.Combine(baseDir, "HostApp")
            ];

            foreach (var d in subDirs)
            {
                try
                {
                    if (!Directory.Exists(d))
                    {
                        Directory.CreateDirectory(d);
                        PrintSuccess($"Created directory: {d}");
                    }
                    else
                    {
                        PrintInfo($"Directory exists: {d}");
                    }
                }
                catch (Exception ex)
                {
                    ExitWithError($"Failed to create directory {d}: {ex.Message}");
                }
            }
        }
    }

    private static void CreateSharedLib(string? targetDir)
    {
        if (targetDir == null) return;
        var projPath = Path.Combine(targetDir, "SharedLib", "SharedLib.csproj");
        var projContent = """

                          <Project Sdk="Microsoft.NET.Sdk">
                            <PropertyGroup>
                              <TargetFramework>netstandard2.1</TargetFramework>
                            </PropertyGroup> 
                          </Project>
                          """.Trim();

        var codePath = Path.Combine(targetDir, "SharedLib", "SharedInterfaces.cs");
        var codeContent = """

                          using Microsoft.AspNetCore.Components;
                          using System.Collections.Generic;

                          public interface IPlugin
                          {
                              string Name { get; }
                              RenderFragment Render { get; }
                              List<IExtension> Extensions { get; }
                          }
                          public interface IExtension
                          {
                              string Name { get; }
                              RenderFragment Render { get; }
                          }
                          public abstract class PluginBase : IPlugin
                          {
                              public abstract string Name { get; }
                              public abstract RenderFragment Render { get; }
                              public List<IExtension> Extensions { get; } = new List<IExtension>();
                          }
                          public abstract class ExtensionBase : IExtension
                          {
                              public abstract string Name { get; }
                              public abstract RenderFragment Render { get; }
                          }

                          """.Trim();

        WriteFileWithRetry(projPath, projContent);
        WriteFileWithRetry(codePath, codeContent);
    }

    private static void CreateSamplePlugin(string? targetDir)
    {
        if (targetDir == null) return;
        var projPath = Path.Combine(targetDir, "SamplePlugin", "SamplePlugin.csproj");
        var projContent = """

                          <Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">
                            <PropertyGroup>
                              <TargetFramework>net8.0</TargetFramework>
                              <Nullable>enable</Nullable>
                            </PropertyGroup>
                            <ItemGroup> 
                              <PackageReference Include="Terminal.Gui" Version="1.4.0" />
                            </ItemGroup>
                          </Project>
                          """.Trim();

        var codePath = Path.Combine(targetDir, "SamplePlugin", "SamplePlugin.cs");
        var codeContent = """

                          using Microsoft.AspNetCore.Components;
                          using SharedLib;
                          using System.Collections.Generic;

                          public class SamplePlugin : PluginBase
                          {
                              public override string Name => "Sample Plugin";

                              public override RenderFragment Render => builder =>
                              {
                                  builder.OpenElement(0, "div");
                                  builder.AddContent(1, "This is the Sample Plugin UI.");
                                  builder.CloseElement();
                                  foreach (var ext in Extensions)
                                  {
                                      builder.AddContent(2, ext.Render);
                                  }
                              };
                          }

                          """.Trim();

        WriteFileWithRetry(projPath, projContent);
        WriteFileWithRetry(codePath, codeContent);
    }

    private static void CreateHostApp(string? targetDir)
    {
        if (targetDir != null)
        {
            var projPath = Path.Combine(targetDir, "HostApp", "HostApp.csproj");
            var projContent = """

                              <Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">
                                <PropertyGroup>
                                  <TargetFramework>net8.0</TargetFramework>
                                  <Nullable>enable</Nullable>
                                </PropertyGroup> 
                                <ItemGroup> 
                                  <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="7.0.0" />
                                  <PackageReference Include="SharedLib" Version="1.0.0" />
                                </ItemGroup>
                              </Project>
                              """.Trim();

            var programPath = Path.Combine(targetDir, "HostApp", "Program.cs");
            var programCode = """

                              using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
                              using Microsoft.Extensions.DependencyInjection;
                              using System.Threading.Tasks;

                              public class Program
                              {
                                  public static async Task Main(string[] args)
                                  {
                                      var builder = WebAssemblyHostBuilder.CreateDefault(args);
                                      builder.RootComponents.Add(typeof(App), "#app");
                                      await builder.Build().RunAsync();
                                  }
                              }

                              """.Trim();

            WriteFileWithRetry(projPath, projContent);
            WriteFileWithRetry(programPath, programCode);
        }
    }

    private static bool RunDotnetRestore(string? targetDir)
    {
        if (string.IsNullOrEmpty(targetDir))
            return false;
        
        PrintInfo("Running dotnet restore...");
        
        // Create a solution file to make it easier to build all projects
        string slnPath = Path.Combine(targetDir, "PluginSolution.sln");
        if (!File.Exists(slnPath))
        {
            // Create a new solution
            if (!RunProcessWithSpinner("dotnet", $"new sln -n PluginSolution -o \"{targetDir}\""))
            {
                PrintError("Failed to create solution file.");
                return false;
            }
            
            // Add projects to the solution
            string[] projectPaths = {
                Path.Combine(targetDir, "SharedLib", "SharedLib.csproj"),
                Path.Combine(targetDir, "SamplePlugin", "SamplePlugin.csproj"),
                Path.Combine(targetDir, "HostApp", "HostApp.csproj")
            };
            
            foreach (string projectPath in projectPaths)
            {
                if (!RunProcessWithSpinner("dotnet", $"sln \"{slnPath}\" add \"{projectPath}\""))
                {
                    PrintError($"Failed to add {projectPath} to solution.");
                    return false;
                }
            }
        }
        
        // Now restore the solution
        return RunProcessWithSpinner("dotnet", $"restore \"{slnPath}\"");
    }

    private static bool RunDotnetBuild(string? targetDir)
    {
        if (string.IsNullOrEmpty(targetDir))
            return false;
        
        PrintInfo("Running dotnet build...");
        string slnPath = Path.Combine(targetDir, "PluginSolution.sln");
        
        if (!File.Exists(slnPath))
        {
            PrintError("Solution file not found. Restore may have failed.");
            return false;
        }
        
        return RunProcessWithSpinner("dotnet", $"build \"{slnPath}\" -c Release");
    }

    private static bool RunProcessWithSpinner(string command, string args)
    {
        try
        {
            using var process = new Process();
            process.StartInfo.FileName = command;
            process.StartInfo.Arguments = args;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.RedirectStandardError = true;
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.CreateNoWindow = true;

            process.OutputDataReceived += (_, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    WriteLine(e.Data);
                }
            };
            process.ErrorDataReceived += (_, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    Error.WriteLine(e.Data);
                }
            };

            process.Start();
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            while (!process.WaitForExit(100))
            {
                ShowSpinner();
            }
            ClearSpinner();

            return process.ExitCode == 0;
        }
        catch (Exception ex)
        {
            PrintError($"Failed to run command '{command} {args}': {ex.Message}");
            return false;
        }
    }

    private static void ShowSpinner()
    {
        Write(SpinnerSequence[_spinnerIndex]);
        SetCursorPosition(CursorLeft - 1, CursorTop);
        _spinnerIndex = (_spinnerIndex + 1) % SpinnerSequence.Length;
    }

    private static void ClearSpinner()
    {
        Write(" ");
        SetCursorPosition(CursorLeft - 1, CursorTop);
    }

    private static void WriteColoredLine(string text, ConsoleColor color)
    {
        var originalColor = ForegroundColor;
        ForegroundColor = color;
        WriteLine(text);
        ForegroundColor = originalColor;
    }

    private static void PrintError(string message)
    {
        var orig = ForegroundColor;
        ForegroundColor = ConsoleColor.Red;
        Error.WriteLine("[ERROR] " + message);
        ForegroundColor = orig;
    }

    private static void PrintInfo(string message)
    {
        var orig = ForegroundColor;
        ForegroundColor = ConsoleColor.Yellow;
        WriteLine("[INFO] " + message);
        ForegroundColor = orig;
    }

    private static void PrintSuccess(string message)
    {
        var orig = ForegroundColor;
        ForegroundColor = ConsoleColor.Green;
        WriteLine("[SUCCESS] " + message);
        ForegroundColor = orig;
    }

    private static void WriteFileWithRetry(string path, string content)
    {
        for (var i = 0; i < 5; i++)
        {
            try
            {
                File.WriteAllText(path, content, Encoding.UTF8);
                PrintSuccess($"Wrote file: {path}");
                break;
            }
            catch (IOException)
            {
                Thread.Sleep(100);
            }
            catch (Exception ex)
            {
                ExitWithError($"Failed to write file {path}: {ex.Message}");
            }
        }
    }

    private static void ExitWithError(string message)
    {
        PrintError(message);
        //Environment.Exit(1);
    }
}