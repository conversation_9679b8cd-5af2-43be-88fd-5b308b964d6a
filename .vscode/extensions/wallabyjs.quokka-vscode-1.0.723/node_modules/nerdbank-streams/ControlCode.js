"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Signals what kind of frame is being transmitted.
 */
var ControlCode;
(function (ControlCode) {
    /**
     * A channel is proposed to the remote party.
     */
    ControlCode[ControlCode["Offer"] = 0] = "Offer";
    /**
     * A channel proposal has been accepted.
     */
    ControlCode[ControlCode["OfferAccepted"] = 1] = "OfferAccepted";
    /**
     * The payload of the frame is a payload intended for channel consumption.
     */
    ControlCode[ControlCode["Content"] = 2] = "Content";
    /**
     * Sent after all bytes have been transmitted on a given channel. Either or both sides may send this.
     * A channel may be automatically closed when each side has both transmitted and received this message.
     */
    ControlCode[ControlCode["ContentWritingCompleted"] = 3] = "ContentWritingCompleted";
    /**
     * Sent when a channel is closed, an incoming offer is rejected, or an outgoing offer is canceled.
     */
    ControlCode[ControlCode["ChannelTerminated"] = 4] = "ChannelTerminated";
    /**
     * Sent when a channel has finished processing data received from the remote party, allowing them to send more data.
     */
    ControlCode[ControlCode["ContentProcessed"] = 5] = "ContentProcessed";
})(ControlCode = exports.ControlCode || (exports.ControlCode = {}));
//# sourceMappingURL=ControlCode.js.map