<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="cloudcode" Version="2.31.1" Publisher="googlecloudtools" />
			<DisplayName>Google Cloud Code</DisplayName>
			<Description xml:space="preserve">Tools for Google Cloud</Description>
			<Tags>AI Assistant,Apigee,Artifact Registry,Cloud Build,Cloud Functions,Cloud Run,Cloud SDK,Code Completion,Code Generation,Compute Engine,Container Registry,Containers,Dataproc,BigQuery Notebooks,Cloud Storage,BigQuery Datasets,GCS,Docker,Gemini Code Assist,Duet AI,Duet,GCE,GCF,GCP,Google Cloud,Google,Kubernetes,LLM,Secret Manager,Skaffold,gcloud,k8s,knative,minikube,snippet,debuggers,json,ignore,Log,log,__ext_log</Tags>
			<Categories>Snippets,Linters,Debuggers,Other</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.82.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="google.geminicodeassist" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/GoogleCloudPlatform/cloud-code-vscode.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/GoogleCloudPlatform/cloud-code-vscode.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/GoogleCloudPlatform/cloud-code-vscode.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/GoogleCloudPlatform/cloud-code-vscode/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/GoogleCloudPlatform/cloud-code-vscode#readme" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				<Property Id="Microsoft.VisualStudio.Services.EnableMarketplaceQnA" Value="false" />
				
			</Properties>
			<License>extension/LICENSE.txt</License>
			<Icon>extension/images/logo.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/README.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/CHANGELOG.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/images/logo.png" Addressable="true" />
		</Assets>
	</PackageManifest>