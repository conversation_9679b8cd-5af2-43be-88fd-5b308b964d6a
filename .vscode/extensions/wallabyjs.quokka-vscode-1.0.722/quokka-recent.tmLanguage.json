{"scopeName": "source.quokka-recent", "patterns": [{"include": "#javascript_file_fragment"}, {"comment": "file", "match": "⁠{1}[^⁠]+⁠{1}", "name": "string.value markup.underline"}, {"comment": "date", "match": " {1}[^ ]+ {1}", "name": "constant.numeric.js"}], "repository": {"javascript_file_fragment": {"begin": "​", "end": "​", "patterns": [{"begin": ".*", "while": "(^|\\G)(?!\\s*(​))", "patterns": [{"include": "source.ts"}, {"include": "source.js"}, {"include": "source.jsx"}, {"include": "source.tsx"}]}]}}}