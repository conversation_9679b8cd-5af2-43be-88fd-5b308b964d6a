{"name": "cancellationtoken", "version": "2.2.0", "description": "A composable token for cancelling asynchronous operations.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/conradreuter/cancellationtoken#readme", "repository": "conradreuter/cancellationtoken", "bugs": "https://github.com/conradreuter/cancellationtoken/issues", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/", "src/", "README.md"], "keywords": ["abort", "async", "cancel", "cancellation", "job", "promise", "task", "token"], "scripts": {"build": "tsc", "clean": "rimraf dist/", "test": "jest -c jest.json --coverage", "watch": "jest -c jest.json --watch", "example": "ts-node -P examples/tsconfig.json -r tsconfig-paths/register examples/run.ts", "info": "npm-scripts-info", "prepublish": "yarn clean && yarn build"}, "scripts-info": {"build": "Build the project.", "clean": "Clean the build directory.", "example": "Run a usage example.", "test": "Run the unit tests once with code coverage.", "watch": "Run the unit tests in watch mode.", "info": "Print this info."}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.15", "@types/node": "^14.14.6", "all-contributors-cli": "^6.19.0", "jest": "^26.6.1", "npm-scripts-info": "^0.3.9", "prettier": "^2.1.2", "rimraf": "^3.0.2", "ts-jest": "^26.4.3", "ts-node": "^9.0.0", "tsconfig-paths": "^3.5.0", "typescript": "^4.0.5"}, "volta": {"node": "14.15.0", "yarn": "1.22.10"}}