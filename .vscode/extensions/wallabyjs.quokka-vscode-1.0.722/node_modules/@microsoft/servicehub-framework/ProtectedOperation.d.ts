/**
 * Describes an operation and who can perform it
 */
export interface ProtectedOperation {
    /** The moniker representing the operation. */
    operationMoniker: string;
    /** The trust level required to perform this operation. */
    requiredTrustLevel?: number;
}
export declare namespace ProtectedOperation {
    /**
     * Creates a new, immutable [ProtectedOperation](#ProtectedOperation) object.
     * @param operationMoniker The moniker representing the operation
     * @param requiredTrustLevel The trust level required to perform this operation
     */
    function create(operationMoniker: string, requiredTrustLevel?: number): Readonly<ProtectedOperation>;
    /**
     * Checks if one operation is a superset of another operation.
     * @param supersetCandidate The possibly superset operation.
     * @param subsetCandidate The possibly subset operation.
     */
    function isSupersetOf(supersetCandidate: ProtectedOperation, subsetCandidate: ProtectedOperation): boolean;
    /**
     * Tests equality between two protected operations.
     * @param operation1 The first operation to compare.
     * @param operation2 The second operation to compare.
     */
    function equals(operation1?: ProtectedOperation, operation2?: ProtectedOperation): boolean;
}
