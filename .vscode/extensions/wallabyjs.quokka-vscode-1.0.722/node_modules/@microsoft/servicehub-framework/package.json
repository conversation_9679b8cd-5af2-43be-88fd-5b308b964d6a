{"name": "@microsoft/servicehub-framework", "version": "2.6.74", "description": "The distributed ServiceHub Framework", "typings": "index.d.ts", "main": "index.js", "scripts": {"test": "nyc mocha --require ts-node/register js/test/*.js"}, "nyc": {"exclude": ["*/test"]}, "author": "Microsoft", "license": "LICENSE.txt", "repository": {"type": "git", "url": "https://dev.azure.com/devdiv/DevDiv/_git/DevCore", "directory": "src/servicebroker-npm"}, "dependencies": {"await-semaphore": "^0.1.3", "msgpack-lite": "^0.1.26", "nerdbank-streams": "2.5.60", "strict-event-emitter-types": "^2.0.0", "vscode-jsonrpc": "^4.0.0"}, "devDependencies": {"@microsoft/tsdoc": "^0.12.19", "@types/mocha": "^5.2.7", "@types/msgpack-lite": "^0.1.7", "@types/node": "^12.7.4", "eslint": "^6.3.0", "eslint-plugin-node": "^10.0.0", "gulp": "^4.0.2", "gulp-mocha": "^7.0.2", "gulp-msbuild": "^0.6.2", "gulp-tslint": "^8.1.4", "gulp-typescript": "^5.0.1", "mocha": "^6.2.0", "nyc": "^14.1.1", "q": "^1.5.1", "ts-node": "^8.6.2", "tslint": "^6.0.0", "typescript": "^3.7.2", "yargs": "^14.2.0"}}