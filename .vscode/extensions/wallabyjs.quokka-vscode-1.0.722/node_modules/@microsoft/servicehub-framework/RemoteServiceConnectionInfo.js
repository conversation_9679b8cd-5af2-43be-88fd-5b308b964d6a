"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoteServiceConnectionInfo = void 0;
var RemoteServiceConnectionInfo;
(function (RemoteServiceConnectionInfo) {
    /**
     * Indicates if there exists remote service connection information
     * @param info The RemoteServiceConnectionInfo to look into.
     */
    function isEmpty(info) {
        return !info.multiplexingChannelId && !info.requestId; // && !info.pipeName; // pipeName is not yet supported.
    }
    RemoteServiceConnectionInfo.isEmpty = isEmpty;
    /**
     * Checks if a remote service connection is of one of the provided types
     * @param info The RemoteServiceConnectionInfo to look into.
     * @param connections The connections to check against
     */
    function isOneOf(info, connections) {
        if (info.multiplexingChannelId) {
            return connections.indexOf('multiplexing') > -1;
        }
        return false;
    }
    RemoteServiceConnectionInfo.isOneOf = isOneOf;
})(RemoteServiceConnectionInfo = exports.RemoteServiceConnectionInfo || (exports.RemoteServiceConnectionInfo = {}));

// SIG // Begin signature block
// SIG // MIIhgQYJKoZIhvcNAQcCoIIhcjCCIW4CAQExDzANBglg
// SIG // hkgBZQMEAgEFADB3BgorBgEEAYI3AgEEoGkwZzAyBgor
// SIG // BgEEAYI3AgEeMCQCAQEEEBDgyQbOONQRoqMAEEvTUJAC
// SIG // AQACAQACAQACAQACAQAwMTANBglghkgBZQMEAgEFAAQg
// SIG // Nyh9QVdCZzuFSEuLunvfxIzwcVh7gfmZC/zTT8L6m9Sg
// SIG // ggtyMIIE+jCCA+KgAwIBAgITMwAAAyVIsp0Of8X0HwAA
// SIG // AAADJTANBgkqhkiG9w0BAQsFADB+MQswCQYDVQQGEwJV
// SIG // UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
// SIG // UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBv
// SIG // cmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQgQ29kZSBT
// SIG // aWduaW5nIFBDQSAyMDEwMB4XDTIwMDMwNDE4MjkyOVoX
// SIG // DTIxMDMwMzE4MjkyOVowdDELMAkGA1UEBhMCVVMxEzAR
// SIG // BgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
// SIG // bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
// SIG // bjEeMBwGA1UEAxMVTWljcm9zb2Z0IENvcnBvcmF0aW9u
// SIG // MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
// SIG // o6USNjR5nReBPqM/t9oEDRVn4lgP4FtusaT6n9JeO7ff
// SIG // aeyCCneQrkwDHjzWFlx0k/KRX2/MmkzVs+pxAmm4UEwi
// SIG // MQKrQM1aQjVl4rkA3tfHhFKFBTdrIKDIaVO4G9IGYoG4
// SIG // b855k9JqKu6KwRADHK1ugvPPvScqlgYAytDAW6cYyMAb
// SIG // 9Lj4I90eNCLTC0MPS29EgZ64jhD0ChgwjtgCOjEovvsX
// SIG // uWFzpEGaywg3Ok5f3/30859QoviUe2CTHXavH9jXuNqI
// SIG // LFFSFuiR/Eimy0F2/cTlZ+HzIhvW+j4EboAKUtCCgFCP
// SIG // DkENpEq9mtQbChGPQezDVHhQVwe+Dq7rWQIDAQABo4IB
// SIG // eTCCAXUwHwYDVR0lBBgwFgYKKwYBBAGCNz0GAQYIKwYB
// SIG // BQUHAwMwHQYDVR0OBBYEFC3CjwCdK+IVK01UmJqWuUQP
// SIG // rh58MFAGA1UdEQRJMEekRTBDMSkwJwYDVQQLEyBNaWNy
// SIG // b3NvZnQgT3BlcmF0aW9ucyBQdWVydG8gUmljbzEWMBQG
// SIG // A1UEBRMNMjMwODY1KzQ1ODQ5MzAfBgNVHSMEGDAWgBTm
// SIG // /F97uyIAWORyTrX0IXQjMubvrDBWBgNVHR8ETzBNMEug
// SIG // SaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtp
// SIG // L2NybC9wcm9kdWN0cy9NaWNDb2RTaWdQQ0FfMjAxMC0w
// SIG // Ny0wNi5jcmwwWgYIKwYBBQUHAQEETjBMMEoGCCsGAQUF
// SIG // BzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtp
// SIG // L2NlcnRzL01pY0NvZFNpZ1BDQV8yMDEwLTA3LTA2LmNy
// SIG // dDAMBgNVHRMBAf8EAjAAMA0GCSqGSIb3DQEBCwUAA4IB
// SIG // AQBcc+DvnFngaPOnuRCJolHiV6CeYbicB+pd2FAS4Qbf
// SIG // rfrhOsw7QapsoHgKvqjLN6LWrnE5julRMi0QAmu2MfMc
// SIG // QyyuzAHJ8cAGOkmI3IhKzbA39bQKXmZAiCDRVv/EtkCj
// SIG // EkF3+DiaJY1h6NU5J4i/SEZs74jfZeNjWDSQfYu9Xs8T
// SIG // /umTeDw1ovEFXu9HRgi5fTtkcT0BYRHJd41nQruQtRQ0
// SIG // YOqjKXMd6rHkEdkBePneBBP1ENYd2HkoW7bs8X9JLGwZ
// SIG // 0KlVNmSfRPxiVE1lErGMbCZxvMYNxuOic97bX1RN3KDC
// SIG // yodXx6rCN11yJVc09wSMdzfZy4NfIcDjbbuZMIIGcDCC
// SIG // BFigAwIBAgIKYQxSTAAAAAAAAzANBgkqhkiG9w0BAQsF
// SIG // ADCBiDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
// SIG // bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
// SIG // FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMp
// SIG // TWljcm9zb2Z0IFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9y
// SIG // aXR5IDIwMTAwHhcNMTAwNzA2MjA0MDE3WhcNMjUwNzA2
// SIG // MjA1MDE3WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMK
// SIG // V2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwG
// SIG // A1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD
// SIG // VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAy
// SIG // MDEwMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
// SIG // AQEA6Q5kUHlntcTj/QkATJ6UrPdWaOpE2M/FWE+ppXZ8
// SIG // bUW60zmStKQe+fllguQX0o/9RJwI6GWTzixVhL99COMu
// SIG // K6hBKxi3oktuSUxrFQfe0dLCiR5xlM21f0u0rwjYzIjW
// SIG // axeUOpPOJj/s5v40mFfVHV1J9rIqLtWFu1k/+JC0K4N0
// SIG // yiuzO0bj8EZJwRdmVMkcvR3EVWJXcvhnuSUgNN5dpqWV
// SIG // XqsogM3Vsp7lA7Vj07IUyMHIiiYKWX8H7P8O7YASNUwS
// SIG // pr5SW/Wm2uCLC0h31oVH1RC5xuiq7otqLQVcYMa0Kluc
// SIG // IxxfReMaFB5vN8sZM4BqiU2jamZjeJPVMM+VHwIDAQAB
// SIG // o4IB4zCCAd8wEAYJKwYBBAGCNxUBBAMCAQAwHQYDVR0O
// SIG // BBYEFOb8X3u7IgBY5HJOtfQhdCMy5u+sMBkGCSsGAQQB
// SIG // gjcUAgQMHgoAUwB1AGIAQwBBMAsGA1UdDwQEAwIBhjAP
// SIG // BgNVHRMBAf8EBTADAQH/MB8GA1UdIwQYMBaAFNX2VsuP
// SIG // 6KJcYmjRPZSQW9fOmhjEMFYGA1UdHwRPME0wS6BJoEeG
// SIG // RWh0dHA6Ly9jcmwubWljcm9zb2Z0LmNvbS9wa2kvY3Js
// SIG // L3Byb2R1Y3RzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIz
// SIG // LmNybDBaBggrBgEFBQcBAQROMEwwSgYIKwYBBQUHMAKG
// SIG // Pmh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbS9wa2kvY2Vy
// SIG // dHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3J0MIGd
// SIG // BgNVHSAEgZUwgZIwgY8GCSsGAQQBgjcuAzCBgTA9Bggr
// SIG // BgEFBQcCARYxaHR0cDovL3d3dy5taWNyb3NvZnQuY29t
// SIG // L1BLSS9kb2NzL0NQUy9kZWZhdWx0Lmh0bTBABggrBgEF
// SIG // BQcCAjA0HjIgHQBMAGUAZwBhAGwAXwBQAG8AbABpAGMA
// SIG // eQBfAFMAdABhAHQAZQBtAGUAbgB0AC4gHTANBgkqhkiG
// SIG // 9w0BAQsFAAOCAgEAGnTvV08pe8QWhXi4UNMi/AmdrIKX
// SIG // +DT/KiyXlRLl5L/Pv5PI4zSp24G43B4AvtI1b6/lf3mV
// SIG // d+UC1PHr2M1OHhthosJaIxrwjKhiUUVnCOM/PB6T+DCF
// SIG // F8g5QKbXDrMhKeWloWmMIpPMdJjnoUdD8lOswA8waX/+
// SIG // 0iUgbW9h098H1dlyACxphnY9UdumOUjJN2FtB91TGcun
// SIG // 1mHCv+KDqw/ga5uV1n0oUbCJSlGkmmzItx9KGg5pqdfc
// SIG // wX7RSXCqtq27ckdjF/qm1qKmhuyoEESbY7ayaYkGx0aG
// SIG // ehg/6MUdIdV7+QIjLcVBy78dTMgW77Gcf/wiS0mKbhXj
// SIG // pn92W9FTeZGFndXS2z1zNfM8rlSyUkdqwKoTldKOEdqZ
// SIG // Z14yjPs3hdHcdYWch8ZaV4XCv90Nj4ybLeu07s8n07Ve
// SIG // afqkFgQBpyRnc89NT7beBVaXevfpUk30dwVPhcbYC/GO
// SIG // 7UIJ0Q124yNWeCImNr7KsYxuqh3khdpHM2KPpMmRM19x
// SIG // HkCvmGXJIuhCISWKHC1g2TeJQYkqFg/XYTyUaGBS79ZH
// SIG // maCAQO4VgXc+nOBTGBpQHTiVmx5mMxMnORd4hzbOTsNf
// SIG // svU9R1O24OXbC2E9KteSLM43Wj5AQjGkHxAIwlacvyRd
// SIG // UQKdannSF9PawZSOB3slcUSrBmrm1MbfI5qWdcUxghVn
// SIG // MIIVYwIBATCBlTB+MQswCQYDVQQGEwJVUzETMBEGA1UE
// SIG // CBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe
// SIG // MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgw
// SIG // JgYDVQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBD
// SIG // QSAyMDEwAhMzAAADJUiynQ5/xfQfAAAAAAMlMA0GCWCG
// SIG // SAFlAwQCAQUAoIGuMBkGCSqGSIb3DQEJAzEMBgorBgEE
// SIG // AYI3AgEEMBwGCisGAQQBgjcCAQsxDjAMBgorBgEEAYI3
// SIG // AgEVMC8GCSqGSIb3DQEJBDEiBCC4b4GkkTLGOBBjircP
// SIG // ltcVRRg5GpPWHQjlGBgAKIcKzDBCBgorBgEEAYI3AgEM
// SIG // MTQwMqAUgBIATQBpAGMAcgBvAHMAbwBmAHShGoAYaHR0
// SIG // cDovL3d3dy5taWNyb3NvZnQuY29tMA0GCSqGSIb3DQEB
// SIG // AQUABIIBAFWciy/8TpsiuW4CrWtopfFpBYgMis1UMNsK
// SIG // ggG9MOhQ/GHNe9umkEKxMFrz3YlgnZalKBOU5Kk/sjPC
// SIG // LjQnrNVoKiwmZIv3dF3580L1y6oGY8DQ8bzmlkeQSPft
// SIG // AdV8srIdAvObN2bC7zueFi8CkiJWWFUW9x4OTtnYKFDu
// SIG // HPl8Wb71UcBqQVhMYpwiXtqaiMj3z6pEpOVvOjDO+l0l
// SIG // LWDjBoi1mHR3ZzeIX5LbIRKqwxAVvONccVx5NN3G+vGT
// SIG // x6vHe5vtpLHGrkZcSJjT12FTCD0bpqw2uBCNa3tzWca6
// SIG // SA5obfZiRDHWZfe/VydZSZ9Mih17gSokdLOwmg6bOXqh
// SIG // ghLxMIIS7QYKKwYBBAGCNwMDATGCEt0wghLZBgkqhkiG
// SIG // 9w0BBwKgghLKMIISxgIBAzEPMA0GCWCGSAFlAwQCAQUA
// SIG // MIIBVQYLKoZIhvcNAQkQAQSgggFEBIIBQDCCATwCAQEG
// SIG // CisGAQQBhFkKAwEwMTANBglghkgBZQMEAgEFAAQgn8q7
// SIG // 2ZaS+RrT8TJTi1FBi4JJParHXpoBnSgMRtKej3ICBl7V
// SIG // SZ7pJxgTMjAyMDA2MTExODQ1MjQuOTY5WjAEgAIB9KCB
// SIG // 1KSB0TCBzjELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
// SIG // c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
// SIG // BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEpMCcGA1UE
// SIG // CxMgTWljcm9zb2Z0IE9wZXJhdGlvbnMgUHVlcnRvIFJp
// SIG // Y28xJjAkBgNVBAsTHVRoYWxlcyBUU1MgRVNOOjREMkYt
// SIG // RTNERC1CRUVGMSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt
// SIG // ZS1TdGFtcCBTZXJ2aWNloIIORDCCBPUwggPdoAMCAQIC
// SIG // EzMAAAErk9Dtjgr38EcAAAAAASswDQYJKoZIhvcNAQEL
// SIG // BQAwfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
// SIG // bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
// SIG // FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMd
// SIG // TWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwHhcN
// SIG // MTkxMjE5MDExNTAyWhcNMjEwMzE3MDExNTAyWjCBzjEL
// SIG // MAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
// SIG // EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
// SIG // c29mdCBDb3Jwb3JhdGlvbjEpMCcGA1UECxMgTWljcm9z
// SIG // b2Z0IE9wZXJhdGlvbnMgUHVlcnRvIFJpY28xJjAkBgNV
// SIG // BAsTHVRoYWxlcyBUU1MgRVNOOjREMkYtRTNERC1CRUVG
// SIG // MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBT
// SIG // ZXJ2aWNlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
// SIG // CgKCAQEAlvqLj8BZWleNACW52AA0cBFIXLMSOW2rtR2z
// SIG // DmxJmjrw3yhaQv5ArPzjZGoiKCBOW2bp0dgVyYHVhFGo
// SIG // x1fpjuAlP1KnwVUjXQXEYXkjp3oG1AKMMFzb+zWKHQTX
// SIG // XVIA2X33kPwWBjx5rm7WeoSiaUIFuN2ipjLcA8RPaEAG
// SIG // kgzqFzAhrEK9366CYmcdI/4/Ej/xHEmHyNP8YVHOQvXD
// SIG // L+QPaIyXvXULODcMv/IAde6ypLD3mFmZbaa0vsc29LPT
// SIG // iuJ+NnDdCdg/AiLjoJKLfgqv2n10HVVXv75mj1KaB2Oa
// SIG // 8Z4WMfYm2HfETE+ShQtpau8hupyk6z0TuwBQlMGwHQID
// SIG // AQABo4IBGzCCARcwHQYDVR0OBBYEFCLvaHS4UgO5dJZh
// SIG // Y/PQd5KIwJMFMB8GA1UdIwQYMBaAFNVjOlyKMZDzQ3t8
// SIG // RhvFM2hahW1VMFYGA1UdHwRPME0wS6BJoEeGRWh0dHA6
// SIG // Ly9jcmwubWljcm9zb2Z0LmNvbS9wa2kvY3JsL3Byb2R1
// SIG // Y3RzL01pY1RpbVN0YVBDQV8yMDEwLTA3LTAxLmNybDBa
// SIG // BggrBgEFBQcBAQROMEwwSgYIKwYBBQUHMAKGPmh0dHA6
// SIG // Ly93d3cubWljcm9zb2Z0LmNvbS9wa2kvY2VydHMvTWlj
// SIG // VGltU3RhUENBXzIwMTAtMDctMDEuY3J0MAwGA1UdEwEB
// SIG // /wQCMAAwEwYDVR0lBAwwCgYIKwYBBQUHAwgwDQYJKoZI
// SIG // hvcNAQELBQADggEBAGc/+dilpj5ulWziMmUftKwSVXhw
// SIG // 692mcKeD5ejG1mc2FVuhBCWkuvs5D+bg5zvzvTtEXyif
// SIG // JdNJYky8cNWEEPvioa5jcoWapYbDgwaoYuoQJSdQf//G
// SIG // 1+Fk8x2LG4wMGZjtK2qRRS5flNFFWHvM11WpLuYT4bMR
// SIG // R53Mjad1NUYm0FQjvdxTvBR7yV58gSdfMp/GzJxPFSiz
// SIG // SdQjZigoafz1lY8pL9jibflYTdiKuZMyMrnsHkDooQZI
// SIG // GT2+rKsY2K8Qaiok36Yw3xlQskko60UvODFWDubPlz8m
// SIG // CwxE+8XBlT3qFZwuCs1XkPd80CdaaIIsIyOIjw3whCzN
// SIG // m8ASnMgwggZxMIIEWaADAgECAgphCYEqAAAAAAACMA0G
// SIG // CSqGSIb3DQEBCwUAMIGIMQswCQYDVQQGEwJVUzETMBEG
// SIG // A1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u
// SIG // ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9u
// SIG // MTIwMAYDVQQDEylNaWNyb3NvZnQgUm9vdCBDZXJ0aWZp
// SIG // Y2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0xMDA3MDEyMTM2
// SIG // NTVaFw0yNTA3MDEyMTQ2NTVaMHwxCzAJBgNVBAYTAlVT
// SIG // MRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdS
// SIG // ZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y
// SIG // YXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0
// SIG // YW1wIFBDQSAyMDEwMIIBIjANBgkqhkiG9w0BAQEFAAOC
// SIG // AQ8AMIIBCgKCAQEAqR0NvHcRijog7PwTl/X6f2mUa3RU
// SIG // ENWlCgCChfvtfGhLLF/Fw+Vhwna3PmYrW/AVUycEMR9B
// SIG // GxqVHc4JE458YTBZsTBED/FgiIRUQwzXTbg4CLNC3ZOs
// SIG // 1nMwVyaCo0UN0Or1R4HNvyRgMlhgRvJYR4YyhB50YWeR
// SIG // X4FUsc+TTJLBxKZd0WETbijGGvmGgLvfYfxGwScdJGcS
// SIG // chohiq9LZIlQYrFd/XcfPfBXday9ikJNQFHRD5wGPmd/
// SIG // 9WbAA5ZEfu/QS/1u5ZrKsajyeioKMfDaTgaRtogINeh4
// SIG // HLDpmc085y9Euqf03GS9pAHBIAmTeM38vMDJRF1eFpwB
// SIG // BU8iTQIDAQABo4IB5jCCAeIwEAYJKwYBBAGCNxUBBAMC
// SIG // AQAwHQYDVR0OBBYEFNVjOlyKMZDzQ3t8RhvFM2hahW1V
// SIG // MBkGCSsGAQQBgjcUAgQMHgoAUwB1AGIAQwBBMAsGA1Ud
// SIG // DwQEAwIBhjAPBgNVHRMBAf8EBTADAQH/MB8GA1UdIwQY
// SIG // MBaAFNX2VsuP6KJcYmjRPZSQW9fOmhjEMFYGA1UdHwRP
// SIG // ME0wS6BJoEeGRWh0dHA6Ly9jcmwubWljcm9zb2Z0LmNv
// SIG // bS9wa2kvY3JsL3Byb2R1Y3RzL01pY1Jvb0NlckF1dF8y
// SIG // MDEwLTA2LTIzLmNybDBaBggrBgEFBQcBAQROMEwwSgYI
// SIG // KwYBBQUHMAKGPmh0dHA6Ly93d3cubWljcm9zb2Z0LmNv
// SIG // bS9wa2kvY2VydHMvTWljUm9vQ2VyQXV0XzIwMTAtMDYt
// SIG // MjMuY3J0MIGgBgNVHSABAf8EgZUwgZIwgY8GCSsGAQQB
// SIG // gjcuAzCBgTA9BggrBgEFBQcCARYxaHR0cDovL3d3dy5t
// SIG // aWNyb3NvZnQuY29tL1BLSS9kb2NzL0NQUy9kZWZhdWx0
// SIG // Lmh0bTBABggrBgEFBQcCAjA0HjIgHQBMAGUAZwBhAGwA
// SIG // XwBQAG8AbABpAGMAeQBfAFMAdABhAHQAZQBtAGUAbgB0
// SIG // AC4gHTANBgkqhkiG9w0BAQsFAAOCAgEAB+aIUQ3ixuCY
// SIG // P4FxAz2do6Ehb7Prpsz1Mb7PBeKp/vpXbRkws8LFZslq
// SIG // 3/Xn8Hi9x6ieJeP5vO1rVFcIK1GCRBL7uVOMzPRgEop2
// SIG // zEBAQZvcXBf/XPleFzWYJFZLdO9CEMivv3/Gf/I3fVo/
// SIG // HPKZeUqRUgCvOA8X9S95gWXZqbVr5MfO9sp6AG9LMEQk
// SIG // IjzP7QOllo9ZKby2/QThcJ8ySif9Va8v/rbljjO7Yl+a
// SIG // 21dA6fHOmWaQjP9qYn/dxUoLkSbiOewZSnFjnXshbcOc
// SIG // o6I8+n99lmqQeKZt0uGc+R38ONiU9MalCpaGpL2eGq4E
// SIG // QoO4tYCbIjggtSXlZOz39L9+Y1klD3ouOVd2onGqBooP
// SIG // iRa6YacRy5rYDkeagMXQzafQ732D8OE7cQnfXXSYIghh
// SIG // 2rBQHm+98eEA3+cxB6STOvdlR3jo+KhIq/fecn5ha293
// SIG // qYHLpwmsObvsxsvYgrRyzR30uIUBHoD7G4kqVDmyW9rI
// SIG // DVWZeodzOwjmmC3qjeAzLhIp9cAvVCch98isTtoouLGp
// SIG // 25ayp0Kiyc8ZQU3ghvkqmqMRZjDTu3QyS99je/WZii8b
// SIG // xyGvWbWu3EQ8l1Bx16HSxVXjad5XwdHeMMD9zOZN+w2/
// SIG // XU/pnR4ZOC+8z1gFLu8NoFA12u8JJxzVs341Hgi62jbb
// SIG // 01+P3nSISRKhggLSMIICOwIBATCB/KGB1KSB0TCBzjEL
// SIG // MAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24x
// SIG // EDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv
// SIG // c29mdCBDb3Jwb3JhdGlvbjEpMCcGA1UECxMgTWljcm9z
// SIG // b2Z0IE9wZXJhdGlvbnMgUHVlcnRvIFJpY28xJjAkBgNV
// SIG // BAsTHVRoYWxlcyBUU1MgRVNOOjREMkYtRTNERC1CRUVG
// SIG // MSUwIwYDVQQDExxNaWNyb3NvZnQgVGltZS1TdGFtcCBT
// SIG // ZXJ2aWNloiMKAQEwBwYFKw4DAhoDFQBEDDaSD+f/SfrQ
// SIG // Pt4bL3pZh0NPpqCBgzCBgKR+MHwxCzAJBgNVBAYTAlVT
// SIG // MRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdS
// SIG // ZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y
// SIG // YXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0
// SIG // YW1wIFBDQSAyMDEwMA0GCSqGSIb3DQEBBQUAAgUA4oz2
// SIG // 0jAiGA8yMDIwMDYxMTIyMzA0MloYDzIwMjAwNjEyMjIz
// SIG // MDQyWjB3MD0GCisGAQQBhFkKBAExLzAtMAoCBQDijPbS
// SIG // AgEAMAoCAQACAieiAgH/MAcCAQACAhGjMAoCBQDijkhS
// SIG // AgEAMDYGCisGAQQBhFkKBAIxKDAmMAwGCisGAQQBhFkK
// SIG // AwKgCjAIAgEAAgMHoSChCjAIAgEAAgMBhqAwDQYJKoZI
// SIG // hvcNAQEFBQADgYEAkKKEydZPiFw1KD9iqwlxCiQNdM57
// SIG // yJ4TAbOjLBm48+vTVH6gBh3M6vBkpHyiFoEgckmQy6Ou
// SIG // N47Ymy0pbJdt2dhJP5LYWCvljFOdhTcmLH2p2MH8Wn6H
// SIG // 1A81qmS1l1KxB2Vw9bEVtPaybksyTFFn/ZmBTKjXmI7X
// SIG // cwV/e6E2NnIxggMNMIIDCQIBATCBkzB8MQswCQYDVQQG
// SIG // EwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE
// SIG // BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv
// SIG // cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGlt
// SIG // ZS1TdGFtcCBQQ0EgMjAxMAITMwAAASuT0O2OCvfwRwAA
// SIG // AAABKzANBglghkgBZQMEAgEFAKCCAUowGgYJKoZIhvcN
// SIG // AQkDMQ0GCyqGSIb3DQEJEAEEMC8GCSqGSIb3DQEJBDEi
// SIG // BCD8OUqZBKdMdqlqekGWCfU6D4yd+9NiQyDhBh5rSIQt
// SIG // wDCB+gYLKoZIhvcNAQkQAi8xgeowgecwgeQwgb0EIGQn
// SIG // OeZKhcJTLFzce9iM4ipYx0bovq1MCDcqwtpdG89fMIGY
// SIG // MIGApH4wfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldh
// SIG // c2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNV
// SIG // BAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UE
// SIG // AxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAC
// SIG // EzMAAAErk9Dtjgr38EcAAAAAASswIgQg6b+iuMZaRE0l
// SIG // ZnEbJ4EABHd0DpCJef8z92sVHSfG16MwDQYJKoZIhvcN
// SIG // AQELBQAEggEAbj2CyFD/9NVxvYtImOgdPJcPJlYww//d
// SIG // J7AdapWIgmwMef8U+cRm6W+13Yny4+JA3dlZG08e5tql
// SIG // HNGjPQ8wAyyFdk7HEGzbmGNobf3Lg1TpfxPXDd99BY0o
// SIG // M3lUSFzE9D6uj2wpuwkwgb4Yw0TvibBpMGi5HwWiV/Ef
// SIG // tj04AebJBDNjNiw7mraneybWT1EL3cPUgGnhVo3VN6qA
// SIG // J1XRQPwyGwPMaNB0sUfFgb151V2dGHGb71pFC1p+l6nl
// SIG // 4zy1Du1XP9MmiIKbW4OVe3Usvus+R19vKKfWqpwmgWso
// SIG // vAfMaIw9M1cuqarZlcJsJgMytO2eB92xFra6cH41K9vQLA==
// SIG // End signature block
