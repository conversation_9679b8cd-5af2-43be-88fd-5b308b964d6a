/// <reference types="node" />
import { Channel } from 'nerdbank-streams';
import { MessageConnection } from 'vscode-jsonrpc';
import { Formatters, MessageDelimiters } from './constants';
import { ServiceMoniker } from './ServiceMoniker';
import { RpcConnection, ServiceRpcDescriptor } from './ServiceRpcDescriptor';
import { IDisposable } from './IDisposable';
/**
 * Constructs a JSON RPC message connection to a service
 */
export declare class ServiceJsonRpcDescriptor extends ServiceRpcDescriptor {
    readonly formatter: Formatters;
    readonly messageDelimiter: MessageDelimiters;
    readonly protocol = "json-rpc";
    private readonly connectionFactory;
    /**
     * Initializes a new instance of the [ServiceJsonRpcDescriptor](#ServiceJsonRpcDescriptor) class
     * @param moniker The moniker this descriptor describes
     * @param formatter The formatter to use when sending messages
     * @param messageDelimiter The delimiter to use in separating messages
     */
    constructor(moniker: ServiceMoniker, formatter: Formatters, messageDelimiter: MessageDelimiters);
    constructRpcConnection(pipe: NodeJS.ReadWriteStream | Channel): JsonRpcConnection;
    equals(descriptor: ServiceRpcDescriptor): boolean;
}
export declare class JsonRpcConnection extends RpcConnection {
    readonly messageConnection: MessageConnection;
    constructor(messageConnection: MessageConnection);
    addLocalRpcTarget(rpcTarget: any): void;
    constructRpcClient<T extends object>(): T & IDisposable;
    startListening(): void;
    dispose(): void;
    private static isMethod;
    private static getInstanceMethodNames;
}
