"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !exports.hasOwnProperty(p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
var constants_1 = require("./constants");
Object.defineProperty(exports, "Formatters", { enumerable: true, get: function () { return constants_1.Formatters; } });
Object.defineProperty(exports, "MessageDelimiters", { enumerable: true, get: function () { return constants_1.MessageDelimiters; } });
Object.defineProperty(exports, "RemoteServiceConnections", { enumerable: true, get: function () { return constants_1.RemoteServiceConnections; } });
__exportStar(require("./FrameworkServices"), exports);
var AuthorizationServiceClient_1 = require("./AuthorizationServiceClient");
Object.defineProperty(exports, "AuthorizationServiceClient", { enumerable: true, get: function () { return AuthorizationServiceClient_1.AuthorizationServiceClient; } });
var ProtectedOperation_1 = require("./ProtectedOperation");
Object.defineProperty(exports, "ProtectedOperation", { enumerable: true, get: function () { return ProtectedOperation_1.ProtectedOperation; } });
var RemoteServiceBroker_1 = require("./RemoteServiceBroker");
Object.defineProperty(exports, "RemoteServiceBroker", { enumerable: true, get: function () { return RemoteServiceBroker_1.RemoteServiceBroker; } });
var RemoteServiceConnectionInfo_1 = require("./RemoteServiceConnectionInfo");
Object.defineProperty(exports, "RemoteServiceConnectionInfo", { enumerable: true, get: function () { return RemoteServiceConnectionInfo_1.RemoteServiceConnectionInfo; } });
var ServiceJsonRpcDescriptor_1 = require("./ServiceJsonRpcDescriptor");
Object.defineProperty(exports, "ServiceJsonRpcDescriptor", { enumerable: true, get: function () { return ServiceJsonRpcDescriptor_1.ServiceJsonRpcDescriptor; } });
Object.defineProperty(exports, "JsonRpcConnection", { enumerable: true, get: function () { return ServiceJsonRpcDescriptor_1.JsonRpcConnection; } });
var ServiceMoniker_1 = require("./ServiceMoniker");
Object.defineProperty(exports, "ServiceMoniker", { enumerable: true, get: function () { return ServiceMoniker_1.ServiceMoniker; } });
var ServiceRpcDescriptor_1 = require("./ServiceRpcDescriptor");
Object.defineProperty(exports, "ServiceRpcDescriptor", { enumerable: true, get: function () { return ServiceRpcDescriptor_1.ServiceRpcDescriptor; } });

// SIG // Begin signature block
// SIG // MIIheQYJKoZIhvcNAQcCoIIhajCCIWYCAQExDzANBglg
// SIG // hkgBZQMEAgEFADB3BgorBgEEAYI3AgEEoGkwZzAyBgor
// SIG // BgEEAYI3AgEeMCQCAQEEEBDgyQbOONQRoqMAEEvTUJAC
// SIG // AQACAQACAQACAQACAQAwMTANBglghkgBZQMEAgEFAAQg
// SIG // Nki1WpAmIQk153dO+1RicsiioG8W0/tCKrPQ5y5oyOmg
// SIG // ggt2MIIE/jCCA+agAwIBAgITMwAAAyauzu35vOR7kgAA
// SIG // AAADJjANBgkqhkiG9w0BAQsFADB+MQswCQYDVQQGEwJV
// SIG // UzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH
// SIG // UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBv
// SIG // cmF0aW9uMSgwJgYDVQQDEx9NaWNyb3NvZnQgQ29kZSBT
// SIG // aWduaW5nIFBDQSAyMDEwMB4XDTIwMDMwNDE4MjkyOVoX
// SIG // DTIxMDMwMzE4MjkyOVowdDELMAkGA1UEBhMCVVMxEzAR
// SIG // BgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
// SIG // bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
// SIG // bjEeMBwGA1UEAxMVTWljcm9zb2Z0IENvcnBvcmF0aW9u
// SIG // MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
// SIG // nZc0xLte0Zp4PVs0bsm9sbjvgdQcrPi/jo5HMBqVz3i/
// SIG // v58maYsTXTnKfo5YVLnOEdTQzdMajP+5bGM0UaIFyUWO
// SIG // t9e0afja/7xLEMQRr/B7aYO5HkhHCTmdhZfGGUI6tk21
// SIG // xW4y/3lnK336zimaOVrPFlXcdUGrqyy8UoBvPbAkOutq
// SIG // HQ17LE7N3G0Xj62tBLgulvRiJGyu3SHrlQ6iUDNcrsi/
// SIG // U2BkvrpZWGHZtEbO0+vXPrryzcJvvu+9InEYRdnLdC3S
// SIG // Fi580IVdmpeUP8RxhDO8jbPKI/B8MZwt6/PlamNT5RDS
// SIG // aQdRbeDBU1p75SOn+/bHlMQpNvl3KxUZzQIDAQABo4IB
// SIG // fTCCAXkwHwYDVR0lBBgwFgYKKwYBBAGCNz0GAQYIKwYB
// SIG // BQUHAwMwHQYDVR0OBBYEFF1zu5HCevZdHtlOyC0v4AOr
// SIG // ptwSMFQGA1UdEQRNMEukSTBHMS0wKwYDVQQLEyRNaWNy
// SIG // b3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQx
// SIG // FjAUBgNVBAUTDTIzMDg2NSs0NTg0OTQwHwYDVR0jBBgw
// SIG // FoAU5vxfe7siAFjkck619CF0IzLm76wwVgYDVR0fBE8w
// SIG // TTBLoEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29t
// SIG // L3BraS9jcmwvcHJvZHVjdHMvTWljQ29kU2lnUENBXzIw
// SIG // MTAtMDctMDYuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr
// SIG // BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29t
// SIG // L3BraS9jZXJ0cy9NaWNDb2RTaWdQQ0FfMjAxMC0wNy0w
// SIG // Ni5jcnQwDAYDVR0TAQH/BAIwADANBgkqhkiG9w0BAQsF
// SIG // AAOCAQEAGebsrQzcccEshd0dcqPjhJA8+0XYpGaevZjO
// SIG // fQpgJWyL4IN6ZVOziQ8iF6TPdfVjYQEVv7dgMnk9/DbP
// SIG // 2nV9kfU0pCSQLpZRAEcGyvi2te6YyrWvET0oEUOHiJkj
// SIG // HbExyhv+sVmWiyP7W2nfM3NQCaXvsqY3Nkl19cr7j9N1
// SIG // 5FMhGpNLgGj2BBbfKO1gkiH6m2/mt9Habf/nO+vKMbyI
// SIG // 1leZaX1g6f2BNoMZaQsTxqVPgZyWcdnkAPZwpDaPDhOu
// SIG // i837rVdRlFtYaqnYmGjJjV9trXpU/GOg9m4zThgOH6AS
// SIG // 65iOjlab05DLZFh7H2dCppHvePf1Lv6y/RUoF12zLTCC
// SIG // BnAwggRYoAMCAQICCmEMUkwAAAAAAAMwDQYJKoZIhvcN
// SIG // AQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpX
// SIG // YXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYD
// SIG // VQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xMjAwBgNV
// SIG // BAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRlIEF1
// SIG // dGhvcml0eSAyMDEwMB4XDTEwMDcwNjIwNDAxN1oXDTI1
// SIG // MDcwNjIwNTAxN1owfjELMAkGA1UEBhMCVVMxEzARBgNV
// SIG // BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQx
// SIG // HjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEo
// SIG // MCYGA1UEAxMfTWljcm9zb2Z0IENvZGUgU2lnbmluZyBQ
// SIG // Q0EgMjAxMDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCC
// SIG // AQoCggEBAOkOZFB5Z7XE4/0JAEyelKz3VmjqRNjPxVhP
// SIG // qaV2fG1FutM5krSkHvn5ZYLkF9KP/UScCOhlk84sVYS/
// SIG // fQjjLiuoQSsYt6JLbklMaxUH3tHSwokecZTNtX9LtK8I
// SIG // 2MyI1msXlDqTziY/7Ob+NJhX1R1dSfayKi7VhbtZP/iQ
// SIG // tCuDdMorsztG4/BGScEXZlTJHL0dxFViV3L4Z7klIDTe
// SIG // XaallV6rKIDN1bKe5QO1Y9OyFMjByIomCll/B+z/Du2A
// SIG // EjVMEqa+Ulv1ptrgiwtId9aFR9UQucboqu6Lai0FXGDG
// SIG // tCpbnCMcX0XjGhQebzfLGTOAaolNo2pmY3iT1TDPlR8C
// SIG // AwEAAaOCAeMwggHfMBAGCSsGAQQBgjcVAQQDAgEAMB0G
// SIG // A1UdDgQWBBTm/F97uyIAWORyTrX0IXQjMubvrDAZBgkr
// SIG // BgEEAYI3FAIEDB4KAFMAdQBiAEMAQTALBgNVHQ8EBAMC
// SIG // AYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBTV
// SIG // 9lbLj+iiXGJo0T2UkFvXzpoYxDBWBgNVHR8ETzBNMEug
// SIG // SaBHhkVodHRwOi8vY3JsLm1pY3Jvc29mdC5jb20vcGtp
// SIG // L2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXRfMjAxMC0w
// SIG // Ni0yMy5jcmwwWgYIKwYBBQUHAQEETjBMMEoGCCsGAQUF
// SIG // BzAChj5odHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtp
// SIG // L2NlcnRzL01pY1Jvb0NlckF1dF8yMDEwLTA2LTIzLmNy
// SIG // dDCBnQYDVR0gBIGVMIGSMIGPBgkrBgEEAYI3LgMwgYEw
// SIG // PQYIKwYBBQUHAgEWMWh0dHA6Ly93d3cubWljcm9zb2Z0
// SIG // LmNvbS9QS0kvZG9jcy9DUFMvZGVmYXVsdC5odG0wQAYI
// SIG // KwYBBQUHAgIwNB4yIB0ATABlAGcAYQBsAF8AUABvAGwA
// SIG // aQBjAHkAXwBTAHQAYQB0AGUAbQBlAG4AdAAuIB0wDQYJ
// SIG // KoZIhvcNAQELBQADggIBABp071dPKXvEFoV4uFDTIvwJ
// SIG // nayCl/g0/yosl5US5eS/z7+TyOM0qduBuNweAL7SNW+v
// SIG // 5X95lXflAtTx69jNTh4bYaLCWiMa8IyoYlFFZwjjPzwe
// SIG // k/gwhRfIOUCm1w6zISnlpaFpjCKTzHSY56FHQ/JTrMAP
// SIG // MGl//tIlIG1vYdPfB9XZcgAsaYZ2PVHbpjlIyTdhbQfd
// SIG // UxnLp9Zhwr/ig6sP4GubldZ9KFGwiUpRpJpsyLcfShoO
// SIG // aanX3MF+0Ulwqratu3JHYxf6ptaipobsqBBEm2O2smmJ
// SIG // BsdGhnoYP+jFHSHVe/kCIy3FQcu/HUzIFu+xnH/8IktJ
// SIG // im4V46Z/dlvRU3mRhZ3V0ts9czXzPK5UslJHasCqE5XS
// SIG // jhHamWdeMoz7N4XR3HWFnIfGWleFwr/dDY+Mmy3rtO7P
// SIG // J9O1Xmn6pBYEAackZ3PPTU+23gVWl3r36VJN9HcFT4XG
// SIG // 2Avxju1CCdENduMjVngiJja+yrGMbqod5IXaRzNij6TJ
// SIG // kTNfcR5Ar5hlySLoQiElihwtYNk3iUGJKhYP12E8lGhg
// SIG // Uu/WR5mggEDuFYF3PpzgUxgaUB04lZseZjMTJzkXeIc2
// SIG // zk7DX7L1PUdTtuDl2wthPSrXkizON1o+QEIxpB8QCMJW
// SIG // nL8kXVECnWp50hfT2sGUjgd7JXFEqwZq5tTG3yOalnXF
// SIG // MYIVWzCCFVcCAQEwgZUwfjELMAkGA1UEBhMCVVMxEzAR
// SIG // BgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
// SIG // bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
// SIG // bjEoMCYGA1UEAxMfTWljcm9zb2Z0IENvZGUgU2lnbmlu
// SIG // ZyBQQ0EgMjAxMAITMwAAAyauzu35vOR7kgAAAAADJjAN
// SIG // BglghkgBZQMEAgEFAKCBrjAZBgkqhkiG9w0BCQMxDAYK
// SIG // KwYBBAGCNwIBBDAcBgorBgEEAYI3AgELMQ4wDAYKKwYB
// SIG // BAGCNwIBFTAvBgkqhkiG9w0BCQQxIgQg9g94u1sjOh9F
// SIG // wmVqH8jYIgLTnzGyk/uwbr5d1O3fqtgwQgYKKwYBBAGC
// SIG // NwIBDDE0MDKgFIASAE0AaQBjAHIAbwBzAG8AZgB0oRqA
// SIG // GGh0dHA6Ly93d3cubWljcm9zb2Z0LmNvbTANBgkqhkiG
// SIG // 9w0BAQEFAASCAQBNlZ4kLmVN33PVnPKl3+Zrxf2gV2Wt
// SIG // KQ6FzOcA1XJiyf2RWAG0BF6VXnpLLQsCa0NbM6OHTbak
// SIG // e9dRg0J5um+CDas/UwknBmhlbvlBCabanup0oUgNYnOc
// SIG // CWi2MEn3fChtVLgBo1ebUJjFh6rNTW4WeDBi72a0nRbj
// SIG // BlP39AiTD8+JyndsCNCczRF620UhUlY1UpUC7CNodqCI
// SIG // q59hxlA+PS/+azkBXBqER4417gEovbUd+g1MfJemW4KC
// SIG // 65X22YN/5QO08KVXfg0+irsdYbsixpC2ei2qB/MXcBgv
// SIG // eoYMSiIYYo9UtR8xLWI+Lp7/LlPhLU6dmywGfDtN4bSS
// SIG // HY/9oYIS5TCCEuEGCisGAQQBgjcDAwExghLRMIISzQYJ
// SIG // KoZIhvcNAQcCoIISvjCCEroCAQMxDzANBglghkgBZQME
// SIG // AgEFADCCAVEGCyqGSIb3DQEJEAEEoIIBQASCATwwggE4
// SIG // AgEBBgorBgEEAYRZCgMBMDEwDQYJYIZIAWUDBAIBBQAE
// SIG // IHtaq4+p0sOWWY5qacyFJrPP3+Dcojoh5daxqGeoulEI
// SIG // AgZe1lPxPF0YEzIwMjAwNjExMTg0NTI2LjA1M1owBIAC
// SIG // AfSggdCkgc0wgcoxCzAJBgNVBAYTAlVTMQswCQYDVQQI
// SIG // EwJXQTEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMV
// SIG // TWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQLEyRN
// SIG // aWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0
// SIG // ZWQxJjAkBgNVBAsTHVRoYWxlcyBUU1MgRVNOOjE3OUUt
// SIG // NEJCMC04MjQ2MSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt
// SIG // ZS1TdGFtcCBTZXJ2aWNloIIOPDCCBPEwggPZoAMCAQIC
// SIG // EzMAAAEMqnhu3MxCTMEAAAAAAQwwDQYJKoZIhvcNAQEL
// SIG // BQAwfDELMAkGA1UEBhMCVVMxEzARBgNVBAgTCldhc2hp
// SIG // bmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoT
// SIG // FU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMd
// SIG // TWljcm9zb2Z0IFRpbWUtU3RhbXAgUENBIDIwMTAwHhcN
// SIG // MTkxMDIzMjMxOTE2WhcNMjEwMTIxMjMxOTE2WjCByjEL
// SIG // MAkGA1UEBhMCVVMxCzAJBgNVBAgTAldBMRAwDgYDVQQH
// SIG // EwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y
// SIG // cG9yYXRpb24xLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVs
// SIG // YW5kIE9wZXJhdGlvbnMgTGltaXRlZDEmMCQGA1UECxMd
// SIG // VGhhbGVzIFRTUyBFU046MTc5RS00QkIwLTgyNDYxJTAj
// SIG // BgNVBAMTHE1pY3Jvc29mdCBUaW1lLVN0YW1wIFNlcnZp
// SIG // Y2UwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB
// SIG // AQCrnTXX5epUmZAq2LDf2KB4Qy8ItxnV+itubGwOSmcI
// SIG // 3VKtOEoj6fY+vfOpPMlWB0kUKgqbWSzWC1Ensdovq0OS
// SIG // s7DxcmZ8lrHJACW4JD57jQ0j4DjD67n0bLz0BVjmUk2u
// SIG // YK9rqCjN+DWTHDpptXlZav4+MSk0KyE7iHG/dSqAxwIq
// SIG // dPZhVJnMXUbLsA+5vV9jQ/W80S44Uqs0IQS9YgpGuqx7
// SIG // IEHvcbwoPbLDqN/PRUrE1JEB2ElX+CE7KsO3lr4voLeb
// SIG // WumvyyqKh/eKiG/cA0iA2rDp7H7j4b4Hskxsgdsak915
// SIG // t50vp49u4EKduAmgOffjSTRrDqKPbUa+9SeRAgMBAAGj
// SIG // ggEbMIIBFzAdBgNVHQ4EFgQUCUI6r0MMhrQDSiqAq0zm
// SIG // +O5l4r4wHwYDVR0jBBgwFoAU1WM6XIoxkPNDe3xGG8Uz
// SIG // aFqFbVUwVgYDVR0fBE8wTTBLoEmgR4ZFaHR0cDovL2Ny
// SIG // bC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv
// SIG // TWljVGltU3RhUENBXzIwMTAtMDctMDEuY3JsMFoGCCsG
// SIG // AQUFBwEBBE4wTDBKBggrBgEFBQcwAoY+aHR0cDovL3d3
// SIG // dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNUaW1T
// SIG // dGFQQ0FfMjAxMC0wNy0wMS5jcnQwDAYDVR0TAQH/BAIw
// SIG // ADATBgNVHSUEDDAKBggrBgEFBQcDCDANBgkqhkiG9w0B
// SIG // AQsFAAOCAQEARPfEGD8hn3N05/BsMYrtwreopi3+pQ6V
// SIG // tEHOB42NvfYrzqcZ5EaQF57XR1U4QZZTDoq0F5aHUtDv
// SIG // Rvrj+0u2Ityx/0nNoDINhvWxGYyLl+NFnvndOq5pPxXs
// SIG // 0ntF8S5h+9mW5t9APQxVtTi3Ox1l1i7ETftXYn2k3z2P
// SIG // sagU20CdKcKfUxHEQ0AguC31fN5DNMQOEVhbQ3YM2mFO
// SIG // RE9caOkObCLpa2Qnl+/SJPIHh3AQL7953SUZsUtzK0mg
// SIG // zB9M0x0fqByceUzOyeKiucYVlrk8+JXvxehn0V66kqjx
// SIG // ko0aEsssHkZO2p8d7HmejeKhVKr422G+FfQj9X6Jcmyi
// SIG // mjCCBnEwggRZoAMCAQICCmEJgSoAAAAAAAIwDQYJKoZI
// SIG // hvcNAQELBQAwgYgxCzAJBgNVBAYTAlVTMRMwEQYDVQQI
// SIG // EwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4w
// SIG // HAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xMjAw
// SIG // BgNVBAMTKU1pY3Jvc29mdCBSb290IENlcnRpZmljYXRl
// SIG // IEF1dGhvcml0eSAyMDEwMB4XDTEwMDcwMTIxMzY1NVoX
// SIG // DTI1MDcwMTIxNDY1NVowfDELMAkGA1UEBhMCVVMxEzAR
// SIG // BgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
// SIG // bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
// SIG // bjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAg
// SIG // UENBIDIwMTAwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAw
// SIG // ggEKAoIBAQCpHQ28dxGKOiDs/BOX9fp/aZRrdFQQ1aUK
// SIG // AIKF++18aEssX8XD5WHCdrc+Zitb8BVTJwQxH0EbGpUd
// SIG // zgkTjnxhMFmxMEQP8WCIhFRDDNdNuDgIs0Ldk6zWczBX
// SIG // JoKjRQ3Q6vVHgc2/JGAyWGBG8lhHhjKEHnRhZ5FfgVSx
// SIG // z5NMksHEpl3RYRNuKMYa+YaAu99h/EbBJx0kZxJyGiGK
// SIG // r0tkiVBisV39dx898Fd1rL2KQk1AUdEPnAY+Z3/1ZsAD
// SIG // lkR+79BL/W7lmsqxqPJ6Kgox8NpOBpG2iAg16HgcsOmZ
// SIG // zTznL0S6p/TcZL2kAcEgCZN4zfy8wMlEXV4WnAEFTyJN
// SIG // AgMBAAGjggHmMIIB4jAQBgkrBgEEAYI3FQEEAwIBADAd
// SIG // BgNVHQ4EFgQU1WM6XIoxkPNDe3xGG8UzaFqFbVUwGQYJ
// SIG // KwYBBAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQD
// SIG // AgGGMA8GA1UdEwEB/wQFMAMBAf8wHwYDVR0jBBgwFoAU
// SIG // 1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL
// SIG // oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3Br
// SIG // aS9jcmwvcHJvZHVjdHMvTWljUm9vQ2VyQXV0XzIwMTAt
// SIG // MDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggrBgEF
// SIG // BQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3Br
// SIG // aS9jZXJ0cy9NaWNSb29DZXJBdXRfMjAxMC0wNi0yMy5j
// SIG // cnQwgaAGA1UdIAEB/wSBlTCBkjCBjwYJKwYBBAGCNy4D
// SIG // MIGBMD0GCCsGAQUFBwIBFjFodHRwOi8vd3d3Lm1pY3Jv
// SIG // c29mdC5jb20vUEtJL2RvY3MvQ1BTL2RlZmF1bHQuaHRt
// SIG // MEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAFAA
// SIG // bwBsAGkAYwB5AF8AUwB0AGEAdABlAG0AZQBuAHQALiAd
// SIG // MA0GCSqGSIb3DQEBCwUAA4ICAQAH5ohRDeLG4Jg/gXED
// SIG // PZ2joSFvs+umzPUxvs8F4qn++ldtGTCzwsVmyWrf9efw
// SIG // eL3HqJ4l4/m87WtUVwgrUYJEEvu5U4zM9GASinbMQEBB
// SIG // m9xcF/9c+V4XNZgkVkt070IQyK+/f8Z/8jd9Wj8c8pl5
// SIG // SpFSAK84Dxf1L3mBZdmptWvkx872ynoAb0swRCQiPM/t
// SIG // A6WWj1kpvLb9BOFwnzJKJ/1Vry/+tuWOM7tiX5rbV0Dp
// SIG // 8c6ZZpCM/2pif93FSguRJuI57BlKcWOdeyFtw5yjojz6
// SIG // f32WapB4pm3S4Zz5Hfw42JT0xqUKloakvZ4argRCg7i1
// SIG // gJsiOCC1JeVk7Pf0v35jWSUPei45V3aicaoGig+JFrph
// SIG // pxHLmtgOR5qAxdDNp9DvfYPw4TtxCd9ddJgiCGHasFAe
// SIG // b73x4QDf5zEHpJM692VHeOj4qEir995yfmFrb3epgcun
// SIG // Caw5u+zGy9iCtHLNHfS4hQEegPsbiSpUObJb2sgNVZl6
// SIG // h3M7COaYLeqN4DMuEin1wC9UJyH3yKxO2ii4sanblrKn
// SIG // QqLJzxlBTeCG+SqaoxFmMNO7dDJL32N79ZmKLxvHIa9Z
// SIG // ta7cRDyXUHHXodLFVeNp3lfB0d4wwP3M5k37Db9dT+md
// SIG // Hhk4L7zPWAUu7w2gUDXa7wknHNWzfjUeCLraNtvTX4/e
// SIG // dIhJEqGCAs4wggI3AgEBMIH4oYHQpIHNMIHKMQswCQYD
// SIG // VQQGEwJVUzELMAkGA1UECBMCV0ExEDAOBgNVBAcTB1Jl
// SIG // ZG1vbmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3Jh
// SIG // dGlvbjEtMCsGA1UECxMkTWljcm9zb2Z0IElyZWxhbmQg
// SIG // T3BlcmF0aW9ucyBMaW1pdGVkMSYwJAYDVQQLEx1UaGFs
// SIG // ZXMgVFNTIEVTTjoxNzlFLTRCQjAtODI0NjElMCMGA1UE
// SIG // AxMcTWljcm9zb2Z0IFRpbWUtU3RhbXAgU2VydmljZaIj
// SIG // CgEBMAcGBSsOAwIaAxUAyyD0VD2mA8tcjYt3nPvENLRA
// SIG // Bn2ggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UE
// SIG // CBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe
// SIG // MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSYw
// SIG // JAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0Eg
// SIG // MjAxMDANBgkqhkiG9w0BAQUFAAIFAOKMr58wIhgPMjAy
// SIG // MDA2MTEyMTI2NTVaGA8yMDIwMDYxMjIxMjY1NVowdzA9
// SIG // BgorBgEEAYRZCgQBMS8wLTAKAgUA4oyvnwIBADAKAgEA
// SIG // AgIEfAIB/zAHAgEAAgIRtzAKAgUA4o4BHwIBADA2Bgor
// SIG // BgEEAYRZCgQCMSgwJjAMBgorBgEEAYRZCgMCoAowCAIB
// SIG // AAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEBBQUA
// SIG // A4GBAA/jGI7YotQZjzTkZfg+xd75NxmuSjS+3qDw4K0h
// SIG // fTA0EUXYv3YZRwPt2bBkttTI89CanITP5qRG62r7/cU6
// SIG // WVQB8CDtkvSmgil3vwWA3yMC2fbxrgyKSDJOTpFjNCTA
// SIG // tVph6TTi5JV/tTpD+qM2KfCCubR0boanvo514gh928RW
// SIG // MYIDDTCCAwkCAQEwgZMwfDELMAkGA1UEBhMCVVMxEzAR
// SIG // BgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1v
// SIG // bmQxHjAcBgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlv
// SIG // bjEmMCQGA1UEAxMdTWljcm9zb2Z0IFRpbWUtU3RhbXAg
// SIG // UENBIDIwMTACEzMAAAEMqnhu3MxCTMEAAAAAAQwwDQYJ
// SIG // YIZIAWUDBAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsq
// SIG // hkiG9w0BCRABBDAvBgkqhkiG9w0BCQQxIgQgylzV1As1
// SIG // 1b+jqKHZJ4VQm9SMR1BegH0SH+myE/H0l8cwgfoGCyqG
// SIG // SIb3DQEJEAIvMYHqMIHnMIHkMIG9BCCDkBYpfszX6bb/
// SIG // /5XuqZG+3Ur/DDky67xfMYkGrKBUKTCBmDCBgKR+MHwx
// SIG // CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9u
// SIG // MRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy
// SIG // b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jv
// SIG // c29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMzAAABDKp4
// SIG // btzMQkzBAAAAAAEMMCIEINgTgWMx+RymzDmuwqTdm5Zk
// SIG // 56Ndi651vsyRaZrPegTkMA0GCSqGSIb3DQEBCwUABIIB
// SIG // AD/NEtlyZRIwkqOqDgwg/lWclpKCQlfB8PNfG1lIUHZg
// SIG // C95oHiDSuqynHgBz2EGfgL0bAzR5XDQB1Z5+HAE4tl9I
// SIG // 6yqoK/B+ruUWt3hWzpl/UR5FcXnf9HTE4uPZhgrLUX/M
// SIG // dNnBd135KCYieh3JtppDJW1/gS0yMgCZL3FDnzBeXpgR
// SIG // QpHJliMEzxvl025/FWVyaQhT0MCYC5VER9UMsvF+0n47
// SIG // xoYWBQd5sEidgN16GFszoHjqyyTmqFqCvjl1LojWWEWv
// SIG // DgDT3or5zWtL9LwuYyK4s3YwdW4GIInb24il/ZyRWj/+
// SIG // vV3zKEQboxGzueHTE0xuaopw7OfwUgj5vOQ=
// SIG // End signature block
