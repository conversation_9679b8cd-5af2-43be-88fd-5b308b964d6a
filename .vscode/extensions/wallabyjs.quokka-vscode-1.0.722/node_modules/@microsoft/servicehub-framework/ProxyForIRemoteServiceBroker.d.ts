import CancellationToken from 'cancellationtoken';
import { MessageConnection } from 'vscode-jsonrpc';
import { IDisposable } from './IDisposable';
import { IRemoteServiceBroker } from './IRemoteServiceBroker';
import { RemoteServiceConnectionInfo } from './RemoteServiceConnectionInfo';
import { ServiceActivationOptions } from './ServiceActivationOptions';
import { ServiceBrokerClientMetadata } from './ServiceBrokerClientMetadata';
import { ServiceMoniker } from './ServiceMoniker';
import { ServiceBrokerEmitter } from './IServiceBroker';
declare const ProxyForIRemoteServiceBroker_base: new () => ServiceBrokerEmitter;
/**
 * A wrapper for the [IRemoteServiceBroker](#IRemoteServiceBroker) interface around an RPC connection
 * TODO: remove this when/if we determine the dynamic proxy creator can handle it.
 */
export declare class ProxyForIRemoteServiceBroker extends ProxyForIRemoteServiceBroker_base implements IDisposable, IRemoteServiceBroker {
    private rpc;
    private isDisposedRpc;
    /**
     * Initializes a new instance of the [RemoteRpcServiceBroker](#RemoteRpcServiceBroker) class
     * @param rpc The RPC message connection to use in sending requests
     */
    constructor(rpc: MessageConnection);
    handshake(clientMetadata: ServiceBrokerClientMetadata, cancellationToken?: CancellationToken): Promise<void>;
    requestServiceChannel(moniker: ServiceMoniker, options: ServiceActivationOptions, cancellationToken?: CancellationToken): Promise<RemoteServiceConnectionInfo>;
    cancelServiceRequest(serviceRequestId: string, cancellationToken?: CancellationToken): Promise<void>;
    dispose(): void;
}
export {};
