"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const MultiplexingStream_1 = require("./MultiplexingStream");
const crypto_1 = require("crypto");
const Utilities_1 = require("./Utilities");
const msgpack = require("msgpack-lite");
const Deferred_1 = require("./Deferred");
const FrameHeader_1 = require("./FrameHeader");
class MultiplexingStreamFormatter {
    static getIsOddRandomData() {
        return crypto_1.randomBytes(16);
    }
    static isOdd(localRandom, remoteRandom) {
        let isOdd;
        for (let i = 0; i < localRandom.length; i++) {
            const sent = localRandom[i];
            const recv = remoteRandom[i];
            if (sent > recv) {
                isOdd = true;
                break;
            }
            else if (sent < recv) {
                isOdd = false;
                break;
            }
        }
        if (isOdd === undefined) {
            throw new Error("Unable to determine even/odd party.");
        }
        return isOdd;
    }
}
exports.MultiplexingStreamFormatter = MultiplexingStreamFormatter;
class MultiplexingStreamV1Formatter extends MultiplexingStreamFormatter {
    constructor(stream) {
        super();
        this.stream = stream;
    }
    end() {
        this.stream.end();
    }
    async writeHandshakeAsync() {
        const randomSendBuffer = MultiplexingStreamFormatter.getIsOddRandomData();
        const sendBuffer = Buffer.concat([MultiplexingStreamV1Formatter.protocolMagicNumber, randomSendBuffer]);
        await Utilities_1.writeAsync(this.stream, sendBuffer);
        return randomSendBuffer;
    }
    async readHandshakeAsync(writeHandshakeResult, cancellationToken) {
        const localRandomBuffer = writeHandshakeResult;
        const recvBuffer = await Utilities_1.getBufferFrom(this.stream, MultiplexingStreamV1Formatter.protocolMagicNumber.length + 16, false, cancellationToken);
        for (let i = 0; i < MultiplexingStreamV1Formatter.protocolMagicNumber.length; i++) {
            const expected = MultiplexingStreamV1Formatter.protocolMagicNumber[i];
            const actual = recvBuffer.readUInt8(i);
            if (expected !== actual) {
                throw new Error(`Protocol magic number mismatch. Expected ${expected} but was ${actual}.`);
            }
        }
        const isOdd = MultiplexingStreamFormatter.isOdd(localRandomBuffer, recvBuffer.slice(MultiplexingStreamV1Formatter.protocolMagicNumber.length));
        return { isOdd: isOdd, protocolVersion: { major: 1, minor: 0 } };
    }
    async writeFrameAsync(header, payload) {
        const headerBuffer = new Buffer(7);
        headerBuffer.writeInt8(header.code, 0);
        headerBuffer.writeUInt32BE(header.channelId || 0, 1);
        headerBuffer.writeUInt16BE((payload === null || payload === void 0 ? void 0 : payload.length) || 0, 5);
        await Utilities_1.writeAsync(this.stream, headerBuffer);
        if (payload && payload.length > 0) {
            await Utilities_1.writeAsync(this.stream, payload);
        }
    }
    async readFrameAsync(cancellationToken) {
        const headerBuffer = await Utilities_1.getBufferFrom(this.stream, 7, true, cancellationToken);
        if (headerBuffer === null) {
            return null;
        }
        const header = new FrameHeader_1.FrameHeader(headerBuffer.readInt8(0), headerBuffer.readUInt32BE(1));
        const payloadLength = headerBuffer.readUInt16BE(5);
        const payload = await Utilities_1.getBufferFrom(this.stream, payloadLength);
        return { header: header, payload: payload };
    }
    serializeOfferParameters(offer) {
        const payload = new Buffer(offer.name, MultiplexingStream_1.MultiplexingStream.ControlFrameEncoding);
        if (payload.length > MultiplexingStream_1.MultiplexingStream.framePayloadMaxLength) {
            throw new Error("Name is too long.");
        }
        return payload;
    }
    deserializeOfferParameters(payload) {
        return {
            name: payload.toString(MultiplexingStream_1.MultiplexingStream.ControlFrameEncoding),
        };
    }
    serializerAcceptanceParameters(_) {
        return new Buffer([]);
    }
    deserializerAcceptanceParameters(_) {
        return {};
    }
    serializeContentProcessed(bytesProcessed) {
        throw new Error("Not supported in the V1 protocol.");
    }
    deserializeContentProcessed(payload) {
        throw new Error("Not supported in the V1 protocol.");
    }
}
exports.MultiplexingStreamV1Formatter = MultiplexingStreamV1Formatter;
/**
 * The magic number to send at the start of communication when using v1 of the protocol.
 */
MultiplexingStreamV1Formatter.protocolMagicNumber = new Buffer([0x2f, 0xdf, 0x1d, 0x50]);
class MultiplexingStreamV2Formatter extends MultiplexingStreamFormatter {
    constructor(stream) {
        super();
        this.reader = msgpack.createDecodeStream();
        stream.pipe(this.reader);
        this.writer = stream;
    }
    end() {
        this.writer.end();
    }
    async writeHandshakeAsync() {
        const randomData = MultiplexingStreamFormatter.getIsOddRandomData();
        const msgpackObject = [[MultiplexingStreamV2Formatter.ProtocolVersion.major, MultiplexingStreamV2Formatter.ProtocolVersion.minor], randomData];
        await Utilities_1.writeAsync(this.writer, msgpack.encode(msgpackObject));
        return randomData;
    }
    async readHandshakeAsync(writeHandshakeResult, cancellationToken) {
        const handshake = await this.readMessagePackAsync(cancellationToken);
        if (handshake === null) {
            throw new Error("No data received during handshake.");
        }
        return {
            isOdd: MultiplexingStreamFormatter.isOdd(writeHandshakeResult, handshake[1]),
            protocolVersion: { major: handshake[0][0], minor: handshake[0][1] },
        };
    }
    async writeFrameAsync(header, payload) {
        const msgpackObject = [header.code];
        if (header.channelId || (payload && payload.length > 0)) {
            msgpackObject.push(header.channelId);
            if (payload && payload.length > 0) {
                msgpackObject.push(payload);
            }
        }
        await Utilities_1.writeAsync(this.writer, msgpack.encode(msgpackObject));
    }
    async readFrameAsync(cancellationToken) {
        const msgpackObject = await this.readMessagePackAsync(cancellationToken);
        if (msgpackObject === null) {
            return null;
        }
        const header = new FrameHeader_1.FrameHeader(msgpackObject[0], msgpackObject.length > 1 ? msgpackObject[1] : undefined);
        return {
            header: header,
            payload: msgpackObject[2] || Buffer.from([]),
        };
    }
    serializeOfferParameters(offer) {
        const payload = [offer.name];
        if (offer.remoteWindowSize) {
            payload.push(offer.remoteWindowSize);
        }
        return msgpack.encode(payload);
    }
    deserializeOfferParameters(payload) {
        const msgpackObject = msgpack.decode(payload);
        return {
            name: msgpackObject[0],
            remoteWindowSize: msgpackObject[1],
        };
    }
    serializerAcceptanceParameters(acceptance) {
        const payload = [];
        if (acceptance.remoteWindowSize) {
            payload.push(acceptance.remoteWindowSize);
        }
        return msgpack.encode(payload);
    }
    deserializerAcceptanceParameters(payload) {
        const msgpackObject = msgpack.decode(payload);
        return {
            remoteWindowSize: msgpackObject[0],
        };
    }
    serializeContentProcessed(bytesProcessed) {
        return msgpack.encode([bytesProcessed]);
    }
    deserializeContentProcessed(payload) {
        return msgpack.decode(payload)[0];
    }
    async readMessagePackAsync(cancellationToken) {
        const streamEnded = new Deferred_1.Deferred();
        while (true) {
            const readObject = this.reader.read();
            if (readObject === null) {
                const bytesAvailable = new Deferred_1.Deferred();
                this.reader.once("readable", bytesAvailable.resolve.bind(bytesAvailable));
                this.reader.once("end", streamEnded.resolve.bind(streamEnded));
                const endPromise = Promise.race([bytesAvailable.promise, streamEnded.promise]);
                await (cancellationToken ? cancellationToken.racePromise(endPromise) : endPromise);
                if (bytesAvailable.isCompleted) {
                    continue;
                }
                return null;
            }
            return readObject;
        }
    }
}
exports.MultiplexingStreamV2Formatter = MultiplexingStreamV2Formatter;
MultiplexingStreamV2Formatter.ProtocolVersion = { major: 2, minor: 0 };
//# sourceMappingURL=MultiplexingStreamFormatters.js.map