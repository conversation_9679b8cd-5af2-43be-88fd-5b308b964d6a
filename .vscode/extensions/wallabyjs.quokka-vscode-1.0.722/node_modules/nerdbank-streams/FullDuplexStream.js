"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const stream_1 = require("stream");
class FullDuplexStream {
    static CreatePair() {
        const pass1 = new stream_1.PassThrough();
        const pass2 = new stream_1.PassThrough();
        return {
            first: FullDuplexStream.Splice(pass1, pass2),
            second: FullDuplexStream.Splice(pass2, pass1),
        };
    }
    static Splice(readable, writable) {
        const duplex = new stream_1.Duplex({
            write(chunk, encoding, callback) {
                writable.write(chunk, encoding, callback);
            },
            final(callback) {
                writable.end(callback);
            },
        });
        // All reads and events come directly from the readable stream.
        duplex.read = readable.read.bind(readable);
        duplex.on = readable.on.bind(readable);
        return duplex;
    }
}
exports.FullDuplexStream = FullDuplexStream;
//# sourceMappingURL=FullDuplexStream.js.map