{"version": 3, "file": "MultiplexingStreamFormatters.js", "sourceRoot": "", "sources": ["../src/MultiplexingStreamFormatters.ts"], "names": [], "mappings": ";;AAEA,6DAA0D;AAC1D,mCAAqC;AACrC,2CAAwD;AAExD,wCAAwC;AACxC,yCAAsC;AACtC,+CAA4C;AAY5C,MAAsB,2BAA2B;IAkBnC,MAAM,CAAC,kBAAkB;QAC/B,OAAO,oBAAW,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;IAES,MAAM,CAAC,KAAK,CAAC,WAAmB,EAAE,YAAoB;QAC5D,IAAI,KAA0B,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,GAAG,IAAI,EAAE;gBACb,KAAK,GAAG,IAAI,CAAC;gBACb,MAAM;aACT;iBAAM,IAAI,IAAI,GAAG,IAAI,EAAE;gBACpB,KAAK,GAAG,KAAK,CAAC;gBACd,MAAM;aACT;SACJ;QAED,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;SAC1D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AA1CD,kEA0CC;AAED,MAAa,6BAA8B,SAAQ,2BAA2B;IAM1E,YAA6B,MAA8B;QACvD,KAAK,EAAE,CAAC;QADiB,WAAM,GAAN,MAAM,CAAwB;IAE3D,CAAC;IAED,GAAG;QACC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,MAAM,gBAAgB,GAAG,2BAA2B,CAAC,kBAAkB,EAAE,CAAC;QAC1E,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,6BAA6B,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,CAAC;QACxG,MAAM,sBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC1C,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,oBAA4B,EAAE,iBAAoC;QACvF,MAAM,iBAAiB,GAAW,oBAAoB,CAAC;QACvD,MAAM,UAAU,GAAG,MAAM,yBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,6BAA6B,CAAC,mBAAmB,CAAC,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;QAE7I,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,6BAA6B,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/E,MAAM,QAAQ,GAAG,6BAA6B,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACtE,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,QAAQ,YAAY,MAAM,GAAG,CAAC,CAAC;aAC9F;SACJ;QAED,MAAM,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC,iBAAiB,EAAE,UAAU,CAAC,KAAK,CAAC,6BAA6B,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/I,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAmB,EAAE,OAAgB;QACvD,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;QACnC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACvC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD,YAAY,CAAC,aAAa,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACpD,MAAM,sBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAC5C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,MAAM,sBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC1C;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,iBAAoC;QACrD,MAAM,YAAY,GAAG,MAAM,yBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAClF,IAAI,YAAY,KAAK,IAAI,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,IAAI,yBAAW,CAC1B,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EACxB,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,aAAa,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,MAAM,yBAAa,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;QAChE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;IAChD,CAAC;IAED,wBAAwB,CAAC,KAAsB;QAC3C,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,uCAAkB,CAAC,oBAAoB,CAAC,CAAC;QAChF,IAAI,OAAO,CAAC,MAAM,GAAG,uCAAkB,CAAC,qBAAqB,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;SACxC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,0BAA0B,CAAC,OAAe;QACtC,OAAO;YACH,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,uCAAkB,CAAC,oBAAoB,CAAC;SAClE,CAAC;IACN,CAAC;IAED,8BAA8B,CAAC,CAAuB;QAClD,OAAO,IAAI,MAAM,CAAC,EAAE,CAAC,CAAC;IAC1B,CAAC;IAED,gCAAgC,CAAC,CAAS;QACtC,OAAO,EAAE,CAAC;IACd,CAAC;IAED,yBAAyB,CAAC,cAAsB;QAC5C,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC;IAED,2BAA2B,CAAC,OAAe;QACvC,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACzD,CAAC;;AA5FL,sEA6FC;AA5FG;;GAEG;AACqB,iDAAmB,GAAG,IAAI,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AA2FvF,MAAa,6BAA8B,SAAQ,2BAA2B;IAK1E,YAAY,MAA8B;QACtC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,GAAG;QACC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,MAAM,UAAU,GAAG,2BAA2B,CAAC,kBAAkB,EAAE,CAAC;QACpE,MAAM,aAAa,GAAG,CAAC,CAAC,6BAA6B,CAAC,eAAe,CAAC,KAAK,EAAE,6BAA6B,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,UAAU,CAAC,CAAC;QAC/I,MAAM,sBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QAC7D,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,oBAA4B,EAAE,iBAAoC;QACvF,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QACrE,IAAI,SAAS,KAAK,IAAI,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACzD;QAED,OAAO;YACH,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5E,eAAe,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACtE,CAAC;IACN,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAmB,EAAE,OAAgB;QACvD,MAAM,aAAa,GAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACrD,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/B,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC/B;SACJ;QAED,MAAM,sBAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,iBAAoC;QACrD,MAAM,aAAa,GAAiB,MAAM,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QACvF,IAAI,aAAa,KAAK,IAAI,EAAE;YACxB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,IAAI,yBAAW,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC1G,OAAO;YACH,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;SAC/C,CAAA;IACL,CAAC;IAED,wBAAwB,CAAC,KAAsB;QAC3C,MAAM,OAAO,GAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;SACxC;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,0BAA0B,CAAC,OAAe;QACtC,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO;YACH,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;YACtB,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC;SACrC,CAAC;IACN,CAAC;IAED,8BAA8B,CAAC,UAAgC;QAC3D,MAAM,OAAO,GAAU,EAAE,CAAC;QAC1B,IAAI,UAAU,CAAC,gBAAgB,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;SAC7C;QAED,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,gCAAgC,CAAC,OAAe;QAC5C,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO;YACH,gBAAgB,EAAE,aAAa,CAAC,CAAC,CAAC;SACrC,CAAC;IACN,CAAC;IAED,yBAAyB,CAAC,cAAsB;QAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,2BAA2B,CAAC,OAAe;QACvC,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,iBAAoC;QACnE,MAAM,WAAW,GAAG,IAAI,mBAAQ,EAAQ,CAAC;QACzC,OAAO,IAAI,EAAE;YACT,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,UAAU,KAAK,IAAI,EAAE;gBACrB,MAAM,cAAc,GAAG,IAAI,mBAAQ,EAAQ,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC1E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC/D,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC/E,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAEnF,IAAI,cAAc,CAAC,WAAW,EAAE;oBAC5B,SAAS;iBACZ;gBAED,OAAO,IAAI,CAAC;aACf;YAED,OAAO,UAAU,CAAC;SACrB;IACL,CAAC;;AA1HL,sEA2HC;AA1H2B,6CAAe,GAAY,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC"}