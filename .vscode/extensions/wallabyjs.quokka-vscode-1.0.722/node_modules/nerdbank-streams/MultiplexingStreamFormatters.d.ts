/// <reference types="node" />
import { OfferParameters } from "./OfferParameters";
import { AcceptanceParameters } from "./AcceptanceParameters";
import CancellationToken from "cancellationtoken";
import { FrameHeader } from "./FrameHeader";
export interface Version {
    major: number;
    minor: number;
}
export interface HandshakeResult {
    isOdd: boolean;
    protocolVersion: Version;
}
export declare abstract class MultiplexingStreamFormatter {
    abstract writeHandshakeAsync(): Promise<Buffer>;
    abstract readHandshakeAsync(writeHandshakeResult: Buffer, cancellationToken: CancellationToken): Promise<HandshakeResult>;
    abstract writeFrameAsync(header: FrameHeader, payload?: Buffer): Promise<void>;
    abstract readFrameAsync(cancellationToken: CancellationToken): Promise<{
        header: FrameHeader;
        payload: Buffer;
    } | null>;
    abstract serializeOfferParameters(offer: OfferParameters): Buffer;
    abstract deserializeOfferParameters(payload: Buffer): OfferParameters;
    abstract serializerAcceptanceParameters(acceptance: AcceptanceParameters): Buffer;
    abstract deserializerAcceptanceParameters(payload: Buffer): AcceptanceParameters;
    abstract serializeContentProcessed(bytesProcessed: number): Buffer;
    abstract deserializeContentProcessed(payload: Buffer): number;
    abstract end(): void;
    protected static getIsOddRandomData(): Buffer;
    protected static isOdd(localRandom: Buffer, remoteRandom: Buffer): boolean;
}
export declare class MultiplexingStreamV1Formatter extends MultiplexingStreamFormatter {
    private readonly stream;
    /**
     * The magic number to send at the start of communication when using v1 of the protocol.
     */
    private static readonly protocolMagicNumber;
    constructor(stream: NodeJS.ReadWriteStream);
    end(): void;
    writeHandshakeAsync(): Promise<Buffer>;
    readHandshakeAsync(writeHandshakeResult: Buffer, cancellationToken: CancellationToken): Promise<HandshakeResult>;
    writeFrameAsync(header: FrameHeader, payload?: Buffer): Promise<void>;
    readFrameAsync(cancellationToken: CancellationToken): Promise<{
        header: FrameHeader;
        payload: Buffer;
    } | null>;
    serializeOfferParameters(offer: OfferParameters): Buffer;
    deserializeOfferParameters(payload: Buffer): OfferParameters;
    serializerAcceptanceParameters(_: AcceptanceParameters): Buffer;
    deserializerAcceptanceParameters(_: Buffer): AcceptanceParameters;
    serializeContentProcessed(bytesProcessed: number): Buffer;
    deserializeContentProcessed(payload: Buffer): number;
}
export declare class MultiplexingStreamV2Formatter extends MultiplexingStreamFormatter {
    private static readonly ProtocolVersion;
    private readonly reader;
    private readonly writer;
    constructor(stream: NodeJS.ReadWriteStream);
    end(): void;
    writeHandshakeAsync(): Promise<Buffer>;
    readHandshakeAsync(writeHandshakeResult: Buffer, cancellationToken: CancellationToken): Promise<HandshakeResult>;
    writeFrameAsync(header: FrameHeader, payload?: Buffer): Promise<void>;
    readFrameAsync(cancellationToken: CancellationToken): Promise<{
        header: FrameHeader;
        payload: Buffer;
    } | null>;
    serializeOfferParameters(offer: OfferParameters): Buffer;
    deserializeOfferParameters(payload: Buffer): OfferParameters;
    serializerAcceptanceParameters(acceptance: AcceptanceParameters): Buffer;
    deserializerAcceptanceParameters(payload: Buffer): AcceptanceParameters;
    serializeContentProcessed(bytesProcessed: number): Buffer;
    deserializeContentProcessed(payload: Buffer): number;
    private readMessagePackAsync;
}
