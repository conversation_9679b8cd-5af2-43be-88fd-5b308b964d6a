{"scopeName": "quokka.output", "name": "quokka-output", "patterns": [{"comment": "empty", "match": "​{5}[^​]+​{5}", "name": "entity.name"}, {"comment": "header", "match": "​{4}[^​]+​{4}", "name": "comment"}, {"comment": "context", "match": "​{3}[^​]+​{3}", "name": "entity.name.function"}, {"comment": "string", "match": ".* ", "name": "constant.numeric.js"}, {"comment": "error", "match": ".* ", "name": "invalid"}, {"comment": "link", "match": "​{1}[^​]+​{1}", "name": "storage.type"}, {"comment": "diff", "begin": "​{6}", "end": "​{6}", "name": "entity.name", "patterns": [{"comment": "deletion", "match": "‍{1}[^‍]+‍{1}", "name": "markup.deleted markup.underline"}, {"comment": "insertion", "match": "⁠{1}[^⁠]+⁠{1}", "name": "markup.inserted markup.underline"}]}, {"comment": "code", "match": "", "patterns": [{"include": "source.tsx"}]}]}