<?xml version="1.0" encoding="utf-8"?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
  <Metadata>
    <Identity Language="en-US" Id="quokka-vscode" Version="1.0.722" Publisher="WallabyJs"/>
    <DisplayName>Quokka.js</DisplayName>
    <Description xml:space="preserve">JavaScript and TypeScript playground in your editor.</Description>
    <Tags>scratchpad,playground,JavaScript,TypeScript,REPL,snippet,keybindings,quokka-output,quokka-recent,quokka-timeline,javascript</Tags>
    <Categories>Debuggers,Testing,Other</Categories>
    <GalleryFlags>Public</GalleryFlags>
    <Badges></Badges>
    <Properties>
      <Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.93.0" />
      <Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
      <Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
      <Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
      
      <Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/wallabyjs/quokka/issues" />
      <Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="http://quokkajs.com" />
      
      
      <Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
      
      
    </Properties>
    <License>extension/LICENSE.md</License>
    <Icon>extension/images/logo.png</Icon>
  </Metadata>
  <Installation>
    <InstallationTarget Id="Microsoft.VisualStudio.Code"/>
  </Installation>
  <Dependencies/>
  <Assets>
    <Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
    <Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/README.md" Addressable="true" /><Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.md" Addressable="true" /><Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/images/logo.png" Addressable="true" />
  </Assets>
</PackageManifest>
