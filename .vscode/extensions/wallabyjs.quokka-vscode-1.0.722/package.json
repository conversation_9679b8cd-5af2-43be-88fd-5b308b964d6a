{"name": "quokka-vscode", "displayName": "Quokka.js", "description": "JavaScript and TypeScript playground in your editor.", "version": "1.0.722", "publisher": "WallabyJs", "homepage": "http://quokkajs.com", "license": "License at https://github.com/wallabyjs/quokka/blob/master/EULA.md", "bugs": {"url": "https://github.com/wallabyjs/quokka/issues"}, "repository": {}, "engines": {"vscode": "^1.93.0"}, "keywords": ["scratchpad", "playground", "JavaScript", "TypeScript", "REPL"], "categories": ["Debuggers", "Testing", "Other"], "activationEvents": ["onStartupFinished"], "main": "./index", "icon": "images/logo.png", "capabilities": {"untrustedWorkspaces": {"supported": true, "description": ""}}, "contributes": {"viewsContainers": {"activitybar": [{"id": "wallaby", "title": "<PERSON><PERSON>", "icon": "images/logo-outline.svg"}], "panel": [{"id": "quokka-output", "title": "<PERSON><PERSON><PERSON>", "icon": "images/logo-outline.svg"}]}, "views": {"wallaby": [{"id": "quokkaValueExplorer", "name": "Quokka Value Explorer", "when": "quokka.startedAtLeastOnce && quokka.prevGenGuiEnabled"}], "quokka-output": [{"id": "quokka.output", "name": "Output", "type": "webview", "visibility": "visible", "when": "!quokka.prevGenGuiEnabled && quokka.hasActiveSession"}]}, "commands": [{"command": "quokka.createFile", "title": "New File", "category": "Quokka.js"}, {"command": "quokka.newJavaScript", "title": "JavaScript File", "category": "Quokka.js"}, {"command": "quokka.newTypeScript", "title": "TypeScript File", "category": "Quokka.js"}, {"command": "quokka.newRecentInteractive", "title": "From Recent / Interactive Example", "category": "Quokka.js"}, {"command": "quokka.createJavaScriptFile", "title": "New JavaScript File", "category": "Quokka.js"}, {"command": "quokka.createTypeScriptFile", "title": "New TypeScript File", "category": "Quokka.js"}, {"command": "quokka.createSnippetFromSelection", "title": "Create Snippet from Selection", "category": "Quokka.js"}, {"command": "quokka.openStartView", "title": "Open Start View", "category": "Quokka.js"}, {"command": "quokka.showOutput", "title": "Show Output", "category": "Quokka.js"}, {"command": "quokka.focusOutput", "title": "Focus Output", "category": "Quokka.js"}, {"command": "quokka.showInstrumentedFile", "title": "Show Instrumented Code", "category": "Quokka.js"}, {"command": "quokka.makeQuokkaFromExistingFile", "title": "Start on Current File", "category": "Quokka.js"}, {"command": "quokka.toggle", "title": "Toggle (Start/Stop) on Current File", "category": "Quokka.js"}, {"command": "quokka.runOnSave", "title": "Run on save for Current File", "category": "Quokka.js"}, {"command": "quokka.runOnce", "title": "Run once for Current File", "category": "Quokka.js"}, {"command": "quokka.stopCurrent", "title": "Stop Current", "category": "Quokka.js"}, {"command": "quokka.stopAll", "title": "Stop All", "category": "Quokka.js"}, {"command": "quokka.installMissingPackageToProject", "title": "Install Missing Package into Project", "category": "Quokka.js"}, {"command": "quokka.installMissingPackageToQuokka", "title": "Install Missing Package only for Quokka File", "category": "Quokka.js"}, {"command": "quokka.installQuokkaPlugin", "title": "Install Quokka Plugin", "category": "Quokka.js"}, {"command": "quokka.addImport", "title": "Add Import", "category": "Quokka.js"}, {"command": "quokka.addRequire", "title": "Add Require", "category": "Quokka.js"}, {"command": "quokka.showValue", "title": "Show Value", "category": "Quokka.js"}, {"command": "quokka.showLineValues", "title": "Show Line Value(s)", "category": "Quokka.js"}, {"command": "quokka.showLineTimings", "title": "Show Line Timing(s)", "category": "Quokka.js"}, {"command": "quokka.copyValue", "title": "Copy Value", "category": "Quokka.js"}, {"command": "quokka.goToLineInQuokkaFile", "title": "Focus Active Quokka File", "category": "Quokka.js"}, {"command": "quokka.showLicense", "title": "Manage License Key", "category": "Quokka.js"}, {"command": "quokka.switchToPro", "title": "Switch to 'Pro' Edition", "category": "Quokka.js"}, {"command": "quokka.switchToCommunity", "title": "Switch to 'Community' Edition", "category": "Quokka.js"}, {"command": "quokka.selectWorkspaceFolder", "title": "Select Workspace Folder", "category": "Quokka.js"}, {"command": "quokka.copyExpressionPath", "title": "Copy Path", "category": "Quokka.js"}, {"command": "quokka.copyExpressionData", "title": "Copy Data", "category": "Quokka.js"}, {"command": "quokka.profile", "title": "Profile", "category": "Quokka.js", "icon": {"dark": "images/profile.dark.svg", "light": "images/profile.light.svg"}}, {"command": "quokka.clearValue", "title": "Clear Value", "category": "Quokka.js"}, {"command": "quokka.clearFileV<PERSON>ues", "title": "Clear File Values", "category": "Quokka.js"}, {"command": "quokka.enableShowValueOnSelection", "title": "Toggle Show Value On Selection", "category": "Quokka.js", "icon": {"dark": "images/showValueOnSelectionDisabled.dark.svg", "light": "images/showValueOnSelectionDisabled.light.svg"}}, {"command": "quokka.disableShowValueOnSelection", "title": "Toggle Show Value On Selection", "category": "Quokka.js", "icon": {"dark": "images/showValueOnSelectionEnabled.dark.svg", "light": "images/showValueOnSelectionEnabled.light.svg"}, "when": "!quokka.autoLogEnabled"}, {"command": "quokka.enableShowSingleInlineValue", "title": "Show Last Displayed Value Only", "category": "Quokka.js", "icon": {"dark": "images/showSingleInlineValueDisabled.dark.svg", "light": "images/showSingleInlineValueDisabled.light.svg"}}, {"command": "quokka.disableShowSingleInlineValue", "title": "Show All Displayed Values", "category": "Quokka.js", "icon": {"dark": "images/showSingleInlineValueEnabled.dark.svg", "light": "images/showSingleInlineValueEnabled.light.svg"}}, {"command": "quokka.viewRecentFiles", "title": "View Recent Files", "category": "Quokka.js"}, {"command": "quokka.runRecentFile", "title": "Run Recent File", "category": "Quokka.js"}, {"command": "quokka.removeRecentFiles", "title": "Remove Recent Files", "category": "Quokka.js"}, {"command": "quokka.disableAutoLog", "title": "Toggle Auto Log (Disable)", "category": "Quokka.js"}, {"command": "quokka.enableAutoLog", "title": "Toggle Auto Log (Enable)", "category": "Quokka.js"}, {"command": "quokka.revealInValueExplorer", "title": "Reveal in Value Explorer", "category": "Quokka.js"}, {"command": "quokka.stopShowingOutputCodeLens", "title": "Stop Displaying Code Lens in Output", "category": "Quokka.js"}, {"command": "quokka.editSessionSettings", "title": "Edit Current Quokka Session Settings", "category": "Quokka.js"}, {"command": "quokka.debug", "title": "Start Time Machine On The Current Line", "category": "Quokka.js"}, {"command": "quokka.debugAutoPlay", "title": "Start Time Machine With Auto Play", "category": "Quokka.js"}, {"command": "quokka.playTraceNextStep", "title": "Step Into", "category": "Quokka.js", "icon": {"dark": "images/debug-step-into.dark.svg", "light": "images/debug-step-into.light.svg"}}, {"command": "quokka.playTracePrevStep", "title": "Step Back Into", "category": "Quokka.js", "icon": {"dark": "images/debug-step-into-back.dark.svg", "light": "images/debug-step-into-back.light.svg"}}, {"command": "quokka.playTraceForwardToSelection", "title": "Run to Active Line", "category": "Quokka.js", "icon": {"dark": "images/debug-continue.dark.svg", "light": "images/debug-continue.light.svg"}}, {"command": "quokka.playTraceBackwardToSelection", "title": "Run Back to Active Line", "category": "Quokka.js", "icon": {"dark": "images/debug-continue-back.dark.svg", "light": "images/debug-continue-back.light.svg"}}, {"command": "quokka.playTraceForwardToBreakpoint", "title": "Run to Breakpoint", "category": "Quokka.js", "icon": {"dark": "images/debug-continue-breakpoint.dark.svg", "light": "images/debug-continue-breakpoint.light.svg"}}, {"command": "quokka.playTraceBackwardToBreakpoint", "title": "Run Back to Breakpoint", "category": "Quokka.js", "icon": {"dark": "images/debug-continue-back-breakpoint.dark.svg", "light": "images/debug-continue-back-breakpoint.light.svg"}}, {"command": "quokka.playTraceNextStepOver", "title": "Step Over", "category": "Quokka.js", "icon": {"dark": "images/debug-step-over.dark.svg", "light": "images/debug-step-over.light.svg"}}, {"command": "quokka.playTracePrevStepOver", "title": "Step Back Over", "category": "Quokka.js", "icon": {"dark": "images/debug-step-over-back.dark.svg", "light": "images/debug-step-over-back.light.svg"}}, {"command": "quokka.playTraceNextStepOut", "title": "Step Out", "category": "Quokka.js", "icon": {"dark": "images/debug-step-out.dark.svg", "light": "images/debug-step-out.light.svg"}}, {"command": "quokka.playTracePrevStepOut", "title": "Step Back Out", "category": "Quokka.js", "icon": {"dark": "images/debug-step-out-back.dark.svg", "light": "images/debug-step-out-back.light.svg"}}, {"command": "quokka.openCallStackFrame", "title": "Open Call Stack Frame", "category": "Quokka.js"}, {"command": "quokka.viewCallStack", "title": "View Call Stack", "category": "Quokka.js", "icon": {"dark": "images/callStack.dark.svg", "light": "images/callStack.light.svg"}}, {"command": "quokka.hideCallStack", "title": "<PERSON>de Call Stack", "category": "Quokka.js"}, {"command": "quokka.stopTraceNavigation", "title": "Stop Time Machine", "category": "Quokka.js", "icon": {"dark": "images/stop.dark.svg", "light": "images/stop.light.svg"}}, {"command": "quokka.revealTraceStep", "title": "Reveal Trace Step", "category": "Quokka.js"}, {"command": "quokka.autoPlayCode", "title": "Auto Play Code", "category": "Quokka.js"}, {"command": "quokka.pauseCodeExecution", "title": "Pause Code Execution", "category": "Quokka.js"}, {"command": "quokka.viewCodeStory", "title": "View Code Story", "category": "Quokka.js"}, {"command": "quokka.share", "title": "Share", "category": "Quokka.js"}, {"command": "quokka.allowFileSnapsExecution", "title": "Allow File Snaps Execution", "category": "Quokka.js"}, {"command": "quokka.insertSnapOutput", "title": "Insert Snap Output", "category": "Quokka.js"}, {"command": "quokka.deleteSnapOutput", "title": "Delete Snap Output", "category": "Quokka.js"}, {"command": "quokka.stopFileSnapsDiscovery", "title": "Stop Snaps Discovery in Current File", "category": "Quokka.js"}, {"command": "quokka.startFileSnapsDiscovery", "title": "Start Snaps Discovery in Current File", "category": "Quokka.js"}, {"command": "quokka.selectAction", "title": "Select Action", "category": "Quokka.js"}, {"command": "quokka.showLogs", "title": "Show Logs", "category": "Quokka.js"}, {"command": "quokka.hoverCopy", "title": "Copy Hover Value to Clipboard", "category": "Quokka.js"}, {"command": "quokka.exploreEntity", "title": "Explore Value", "category": "Quokka.js"}, {"command": "quokka.showQuokkaRemotePort", "title": "Show Quokka Remote Port", "category": "Quokka.js"}, {"command": "quokka.editQuokkaSnippets", "title": "<PERSON> <PERSON><PERSON><PERSON>", "category": "Quokka.js"}], "menus": {"commandPalette": [{"command": "quokka.createFile", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.newJavaScript", "when": "false"}, {"command": "quokka.newTypeScript", "when": "false"}, {"command": "quokka.newRecentInteractive", "when": "false"}, {"command": "quokka.createJavaScriptFile", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.createTypeScriptFile", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.showInstrumentedFile", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.showOutput", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.focusOutput", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession && !quokka.prevGenGuiEnabled"}, {"command": "quokka.makeQuokkaFromExistingFile", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.toggle", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.runOnSave", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.runOnce", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.stopCurrent", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.stopAll", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.installMissingPackageToProject", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.installMissingPackageToQuokka", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.showValue", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.copyValue", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.showLineValues", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.showLineTimings", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.goToLineInQuokkaFile", "when": "!quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.selectWorkspaceFolder", "when": "!quokka.isLiveShareClient && workspaceFolderCount >= 2"}, {"command": "quokka.installQuokkaPlugin", "when": "false"}, {"command": "quokka.addImport", "when": "false"}, {"command": "quokka.addRequire", "when": "false"}, {"command": "quokka.copyExpressionPath", "when": "false"}, {"command": "quokka.copyExpressionData", "when": "false"}, {"command": "quokka.profile", "when": "!quokka.isLiveShareClient && quokka.isProfilingEnabled && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.clearValue", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.clearFileV<PERSON>ues", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.enableShowValueOnSelection", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.showValueOnSelectionEnabled && !quokka.autoLogEnabled"}, {"command": "quokka.disableShowValueOnSelection", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.showValueOnSelectionEnabled && !quokka.autoLogEnabled"}, {"command": "quokka.enableShowSingleInlineValue", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.showSingleInlineValueEnabled && !quokka.autoLogEnabled"}, {"command": "quokka.disableShowSingleInlineValue", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.showSingleInlineValueEnabled && !quokka.autoLogEnabled"}, {"command": "quokka.viewRecentFiles", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.runRecentFile", "when": "false"}, {"command": "quokka.removeRecentFiles", "when": "false"}, {"command": "quokka.disableAutoLog", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.autoLogEnabled"}, {"command": "quokka.enableAutoLog", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.autoLogEnabled"}, {"command": "quokka.revealInValueExplorer", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.prevGenGuiEnabled"}, {"command": "quokka.stopShowingOutputCodeLens", "when": "false"}, {"command": "quokka.debug", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.traceBeingNavigated && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.editSessionSettings", "when": "quokka.isPro && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.traceBeingNavigated"}, {"command": "quokka.debugAutoPlay", "when": "quokka.prevGenGuiEnabled && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.traceBeingNavigated && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.playTraceNextStep", "when": "false"}, {"command": "quokka.playTracePrevStep", "when": "false"}, {"command": "quokka.playTraceForwardToSelection", "when": "false"}, {"command": "quokka.playTraceBackwardToSelection", "when": "false"}, {"command": "quokka.playTraceForwardToBreakpoint", "when": "false"}, {"command": "quokka.playTraceBackwardToBreakpoint", "when": "false"}, {"command": "quokka.playTraceNextStepOver", "when": "false"}, {"command": "quokka.playTracePrevStepOver", "when": "false"}, {"command": "quokka.playTraceNextStepOut", "when": "false"}, {"command": "quokka.playTracePrevStepOut", "when": "false"}, {"command": "quokka.openCallStackFrame", "when": "false"}, {"command": "quokka.viewCallStack", "when": "false"}, {"command": "quokka.hideCallStack", "when": "false"}, {"command": "quokka.stopTraceNavigation", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.revealTraceStep", "when": "false"}, {"command": "quokka.autoPlayCode", "when": "quokka.prevGenGuiEnabled && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.pauseCodeExecution", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && quokka.traceBeingAutoPlayed && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.viewCodeStory", "when": "quokka.isCodeStoryEnabled && quokka.isPro && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.share", "when": "activeEditor && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka && editorLangId != svelte && editorLangId != vue"}, {"command": "quokka.allowFileSnapsExecution", "when": "activeEditor && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka && !quokka.snapsExecutionAllowed"}, {"command": "quokka.insertSnapOutput", "when": "activeEditor && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka && quokka.snapsExecutionAllowed"}, {"command": "quokka.deleteSnapOutput", "when": "activeEditor && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka && quokka.snapsExecutionAllowed"}, {"command": "quokka.stopFileSnapsDiscovery", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.startFileSnapsDiscovery", "when": "activeEditor && !quokka.isLiveShareClient"}, {"command": "quokka.selectAction", "when": "false"}, {"command": "quokka.showLogs", "when": "quokka.debug"}, {"command": "quokka.hoverCopy", "when": "false"}, {"command": "quokka.exploreEntity", "when": "!quokka.prevGenGuiEnabled && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.showQuokkaRemotePort", "when": "!quokka.prevGenGuiEnabled && activeEditor && !quokka.isLiveShareClient && quokka.remote"}, {"command": "quokka.editQuokkaSnippets", "when": "quokka.isPro && !quokka.isLiveShareClient"}, {"command": "quokka.createSnippetFromSelection", "when": "quokka.isPro && !quokka.isLiveShareClient && editorHasSelection && activeEditor"}], "view/title": [{"command": "quokka.viewCallStack", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && quokka.isAllowedDebuggerEditAndContinue", "group": "navigation@1"}, {"command": "quokka.playTraceBackwardToSelection", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@2"}, {"command": "quokka.playTracePrevStepOut", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@3"}, {"command": "quokka.playTracePrevStep", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@4"}, {"command": "quokka.playTracePrevStepOver", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@5"}, {"command": "quokka.stopTraceNavigation", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@6"}, {"command": "quokka.playTraceNextStepOver", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@7"}, {"command": "quokka.playTraceNextStep", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@8"}, {"command": "quokka.playTraceNextStepOut", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@9"}, {"command": "quokka.playTraceForwardToSelection", "when": "view =~ /quokka.output/ && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed", "group": "navigation@10"}], "view/item/context": [{"command": "quokka.copyExpressionPath", "when": "viewItem =~ /QuokkaAllowToCopyPath/"}, {"command": "quokka.copyExpressionData", "when": "viewItem =~ /QuokkaAllowToCopyData/"}], "file/newFile": [{"command": "quokka.newJavaScript", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.newTypeScript", "when": "!quokka.isLiveShareClient"}, {"command": "quokka.newRecentInteractive", "when": "!quokka.isLiveShareClient"}], "editor/title": [{"command": "quokka.playTraceBackwardToSelection", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-998"}, {"command": "quokka.playTracePrevStepOut", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-997"}, {"command": "quokka.playTracePrevStep", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-996"}, {"command": "quokka.playTracePrevStepOver", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-995"}, {"command": "quokka.stopTraceNavigation", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-994"}, {"command": "quokka.playTraceNextStepOver", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-993"}, {"command": "quokka.playTraceNextStep", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-992"}, {"command": "quokka.playTraceNextStepOut", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-991"}, {"command": "quokka.playTraceForwardToSelection", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-990"}, {"command": "quokka.playTraceBackwardToBreakpoint", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && quokka.hasAnyEnabledBreakpointsInActiveEditor && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && quokka.hasAnyEnabledBreakpointsInActiveEditor && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-989"}, {"command": "quokka.playTraceForwardToBreakpoint", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && quokka.hasAnyEnabledBreakpointsInActiveEditor && !quokka.traceBeingAutoPlayed && resourceScheme != quokka-code-timeline && quokka.prevGenGuiEnabled || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && quokka.hasAnyEnabledBreakpointsInActiveEditor && !quokka.traceBeingAutoPlayed && resourceScheme == quokka-code-timeline && quokka.prevGenGuiEnabled", "group": "navigation@-988"}]}, "keybindings": [{"command": "quokka.makeQuokkaFromExistingFile", "key": "ctrl+k q", "mac": "cmd+k q", "when": "!terminalFocus && !quokka.isLiveShareClient"}, {"command": "quokka.createJavaScriptFile", "key": "ctrl+k j", "mac": "cmd+k j", "when": "!terminalFocus && !quokka.isLiveShareClient"}, {"command": "quokka.createTypeScriptFile", "key": "ctrl+k t", "mac": "cmd+k t", "when": "!terminalFocus && !quokka.isLiveShareClient"}, {"command": "quokka.createFile", "key": "ctrl+k l", "mac": "cmd+k l", "when": "!terminalFocus && !quokka.isLiveShareClient"}, {"command": "quokka.stopCurrent", "key": "ctrl+k e", "mac": "cmd+k s", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.showValue", "key": "ctrl+k v", "mac": "cmd+k v", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.showLineValues", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.showLineTimings", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.copyValue", "key": "ctrl+k x", "mac": "cmd+k x", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession && quokka.isActiveEditorRunningQuokka"}, {"command": "quokka.installMissingPackageToQuokka", "key": "ctrl+k i", "mac": "cmd+k i", "when": "!terminalFocus && !quokka.isLiveShareClient && quokka.hasActiveSession"}, {"command": "quokka.clearValue", "key": "Escape", "mac": "Escape", "when": "quokka.isActiveEditorRunningQuokka && quokka.lineHasRemovableInlineValues && editorTextFocus && !suggestWidgetVisible && !findWidgetVisible && !renameInputVisible && !referenceSearchVisible && !inReferenceSearchEditor && !quickFixWidgetVisible && !parameterHintsVisible && !vim.active || quokka.isActiveEditorRunningQuokka && quokka.lineHasRemovableInlineValues && editorTextFocus && !suggestWidgetVisible && !findWidgetVisible && !renameInputVisible && !referenceSearchVisible && !inReferenceSearchEditor && !quickFixWidgetVisible && !parameterHintsVisible && vim.mode=='Normal'"}, {"command": "quokka.clearFileV<PERSON>ues", "key": "Escape Escape", "mac": "Escape Escape", "when": "quokka.isActiveEditorRunningQuokka && !quokka.lineHasRemovableInlineValues && quokka.fileHasRemovableInlineValues && editorTextFocus && !suggestWidgetVisible && !findWidgetVisible && !renameInputVisible && !referenceSearchVisible && !inReferenceSearchEditor && !quickFixWidgetVisible && !parameterHintsVisible && !editorHasSelection && !vim.active || quokka.isActiveEditorRunningQuokka && !quokka.lineHasRemovableInlineValues && quokka.fileHasRemovableInlineValues && editorTextFocus && !suggestWidgetVisible && !findWidgetVisible && !renameInputVisible && !referenceSearchVisible && !inReferenceSearchEditor && !quickFixWidgetVisible && !parameterHintsVisible && !editorHasSelection && vim.mode=='Normal'"}, {"command": "quokka.stopTraceNavigation", "key": "shift+f5", "mac": "shift+f5", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.debug", "key": "shift+f5", "mac": "shift+f5", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && !quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.debugAutoPlay", "key": "alt+shift+f5", "mac": "alt+shift+f5", "when": "quokka.prevGenGuiEnabled && activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && !quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && !quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceNextStep", "key": "f11", "mac": "f11", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceForwardToSelection", "key": "f5", "mac": "f5", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceForwardToBreakpoint", "key": "f8", "mac": "f8", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceNextStepOver", "key": "f10", "mac": "f10", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceNextStepOut", "key": "shift+f11", "mac": "shift+f11", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTracePrevStep", "key": "ctrl+f11", "mac": "ctrl+f11", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceBackwardToSelection", "key": "ctrl+f5", "mac": "ctrl+f5", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTraceBackwardToBreakpoint", "key": "ctrl+f8", "mac": "ctrl+f8", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTracePrevStepOver", "key": "ctrl+f10", "mac": "ctrl+f10", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}, {"command": "quokka.playTracePrevStepOut", "key": "ctrl+shift+f11", "mac": "ctrl+shift+f11", "when": "activeEditor && !quokka.isLiveShareClient && quokka.isActiveEditorRunningQuokka && quokka.traceBeingNavigated && !inDebugMode || activeEditor && !quokka.isLiveShareClient && quokka.traceBeingNavigated && !inDebugMode && resourceScheme == quokka-code-timeline"}], "languages": [{"id": "quokka-output"}, {"id": "quokka-recent"}, {"id": "quokka-timeline"}], "grammars": [{"language": "quokka-output", "scopeName": "quokka.output", "path": "./quokka-output.tmGrammer.json"}, {"language": "quokka-recent", "scopeName": "source.quokka-recent", "path": "./quokka-recent.tmLanguage.json"}, {"language": "quokka-timeline", "scopeName": "source.quokka-timeline", "path": "./quokka-timeline.tmGrammer.json"}, {"injectTo": ["source.js", "source.jsx", "source.ts", "source.tsx"], "scopeName": "source.quokka-snap", "path": "./quokka-snap.tmLanguage.json"}], "snippets": [{"path": "./.code-snippets"}], "configurationDefaults": {"[quokka-output]": {"editor.unicodeHighlight.invisibleCharacters": false, "editor.unicodeHighlight.excludedCharacters": [], "editor.unicodeHighlight.nonBasicASCII": false, "editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.includeComments": false}, "[quokka-recent]": {"editor.unicodeHighlight.invisibleCharacters": false, "editor.unicodeHighlight.excludedCharacters": [], "editor.unicodeHighlight.nonBasicASCII": false, "editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.includeComments": false}, "[quokka-timeline]": {"editor.unicodeHighlight.invisibleCharacters": false, "editor.unicodeHighlight.excludedCharacters": [], "editor.unicodeHighlight.nonBasicASCII": false, "editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.includeComments": false}}, "configuration": {"type": "object", "title": "Quokka configuration", "properties": {"quokka.suppressExpirationNotifications": {"description": "Stop Quokka expiring license update reminders", "type": "boolean", "default": false}, "quokka.showOutputOnStart": {"description": "Show Quokka output window on start up", "type": "boolean", "default": true}, "quokka.colorizeOutput": {"description": "Colorize Quokka output", "type": "boolean", "default": true, "markdownDeprecationMessage": "**Deprecated**: The setting is only applicable for Quokka v1.", "deprecationMessage": "Deprecated: The setting is only applicable for Quokka v1."}, "quokka.suppressGlyphMarginNotifications": {"description": "Suppress Quokka Glyph Margin Notifications", "type": "boolean", "default": true}, "quokka.lightTheme.log.decorationAttachmentRenderOptions": {"description": "Override Quokka Light Theme Log", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "#0000ff", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.lightTheme.system.decorationAttachmentRenderOptions": {"description": "Override Quokka Light Theme System Log", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "rgb(153, 153, 153)", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.lightTheme.error.decorationAttachmentRenderOptions": {"description": "Override Quokka Light Theme Error", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "#c80000", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.darkTheme.log.decorationAttachmentRenderOptions": {"description": "Override Quokka Dark Theme Log", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "rgba(86, 156, 214, 1)", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.darkTheme.system.decorationAttachmentRenderOptions": {"description": "Override Quokka Dark Theme System Log", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "rgb(153, 153, 153)", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.darkTheme.error.decorationAttachmentRenderOptions": {"description": "Override Quokka Dark Theme Error", "type": "object", "default": {"border": null, "borderColor": null, "fontStyle": null, "fontWeight": null, "textDecoration": null, "color": "#fe536a", "backgroundColor": null, "margin": "1.2em", "width": null, "height": null}}, "quokka.colors": {"description": "Quokka gutter indicators colors (requires a restart after change)", "type": "object", "default": {"covered": "#62b455", "errorPath": "#ffa0a0", "errorSource": "#fe536a", "notCovered": "#cccccc", "partiallyCovered": "#d2a032"}}, "quokka.compactMessageOutput": {"description": "Minimize the number of new line characters between output console messages", "type": "boolean", "default": false, "markdownDeprecationMessage": "**Deprecated**: The setting is only applicable for Quokka v1.", "deprecationMessage": "Deprecated: The setting is only applicable for Quokka v1."}, "quokka.automaticRestart": {"description": "Automatically start on file open if <PERSON><PERSON><PERSON> was last running when the file was closed", "type": "boolean", "default": false}, "quokka.automaticStartRegex": {"description": "Automatically start on file open when the file path matches the regular expression", "type": "string", "default": ""}, "quokka.showStartViewOnFeatureRelease": {"description": "Display information about new features when they are released", "type": "boolean", "default": true}, "quokka.showCodeLensInOutputChannel": {"description": "Display code lens in Quokka output", "type": "boolean", "default": true, "markdownDeprecationMessage": "**Deprecated**: The setting is only applicable for Quokka v1.", "deprecationMessage": "Deprecated: The setting is only applicable for Quokka v1."}, "quokka.codeAutoPlayDelay": {"description": "Code auto-play delay in milliseconds", "type": "number", "default": 1000}, "quokka.untrustedWorkspaceBehavior": {"markdownDescription": "Specifies whether <PERSON><PERSON><PERSON> is allowed to run in an [untrusted workspace](https://code.visualstudio.com/docs/editor/workspace-trust).", "type": "string", "default": "Prompt to allow", "enum": ["Prompt to allow", "Never allow", "Always allow"], "enumDescriptions": ["Prompt for confirmation before starting <PERSON><PERSON><PERSON> in an untrusted workspace.", "Never start Quokka in an untrusted workspace.", "Always start Quokka (without prompts) in an untrusted workspace."]}, "quokka.codeClipAutoUpload": {"description": "Suppress auto upload Code Clip notification", "type": "boolean", "default": false}, "quokka.showValueOnMultilineSelection": {"description": "Show expression value on multiline selection", "type": "boolean", "default": false}, "quokka.syncSettings": {"description": "Synchronize Quokka license details and other settings as a part of VS Code Settings Sync", "type": "boolean", "default": true}, "quokka.snapsAutoDiscovery": {"description": "Automatically discover snaps when a file is opened or edited", "type": "boolean", "default": true}, "quokka.snapsAutoRunConfirmOnOpen": {"description": "Confirm before running existing snaps when a file is opened", "type": "boolean", "default": true}, "quokka.snapsAutoRunConfirmOnEdit": {"description": "Confirm before running newly added snaps when a file is edited. This confirmation will run ALL (existing and newly added) snaps in file.", "type": "boolean", "default": true}, "quokka.textDecorationRenderScope": {"markdownDescription": "(**PRO feature**) Specifies whether text decorations should render for the current line or all lines \n\n _(Always &quot;All Lines&quot; for Community Edition)_", "type": "string", "default": "All lines", "enum": ["All lines", "Current line"], "enumDescriptions": ["Render text decorations for all lines.", "Render text decorations only for the current line."]}, "quokka.rollBackToPreviousGenerationUI": {"description": "Roll back to the previous generation of the Quokka UI v1.0.", "type": "boolean", "default": false}, "quokka.fontSize": {"type": "integer", "default": 13, "markdownDescription": "Controls the font size (in pixels) of the Quokka output panel."}}}}, "scripts": {"prepare": "node .husky/install.mjs", "build": "npm run test && grunt", "pub": "grunt bump-only && npm run pub:prerelease && vsce publish && grunt publish-complete", "pub:prerelease": "npm run build && vsce package", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "test": "npm run compile && jest", "release": "node ./docker/run.js", "pre-release": "node ./docker/run.js --pre-release", "format": "npx prettier --write \"**/[!.]*.{js,ts,cjs,mjs,css,md,html}\"", "format-check": "npx prettier --check \"**/[!.]*.{js,ts,cjs,mjs,css,md,html}\"", "ts-check": "tsc -p . --noEmit"}, "dependencies": {"adm-zip": "0.4.13", "jsonc-parser": "^3.3.1", "plist": "^3.1.0", "sql.js": "1.8.0", "tslib": "^1.9.3", "vsls": "^1.0.4753", "lodash": "3.0.0"}, "devDependencies": {"@babel/core": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.15.0", "@swc/core": "^1.3.102", "@swc/helpers": "^0.5.3", "@types/adm-zip": "^0.4.34", "@types/jest": "^27.0.2", "@types/lodash": "^4.17.12", "@types/node": "^10.17.24", "@types/plist": "^3.0.5", "@types/sinon": "^5.0.5", "@types/vscode": "^1.93.0", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "babel-jest": "^27.3.1", "eslint": "^7.24.0", "eslint-plugin-jest": "^24.3.6", "grunt": "*", "grunt-browserify": "*", "grunt-bump": "0.0.16", "grunt-contrib-clean": "*", "grunt-contrib-compress": "^0.12.0", "grunt-contrib-copy": "*", "grunt-contrib-uglify-es": "*", "grunt-ts": "^6.0.0-beta.16", "husky": "^9.0.11", "jest": "^27.3.1", "lint-staged": "^15.2.7", "prettier": "^3.2.5", "sinon": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vsce": "^1.46.0", "wallaby-core": "file:../wallaby", "wallaby-ui": "file:../wallaby-ui"}, "optionalDependencies": {"aws-sdk": "^2.353.0", "keytar": "^7.9.0"}, "__metadata": {"id": "aae0701b-111f-49c1-9000-abd37a442f28", "publisherDisplayName": "Wallaby.js", "publisherId": "9e43bb4e-4318-4e59-b5c2-df1109a963dd", "size": 77199191, "installedTimestamp": 1747826159541, "targetPlatform": "undefined"}, "lint-staged": {"*.{js,ts,cjs,mjs,css,md,html}": "prettier --write"}}